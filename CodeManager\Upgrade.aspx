<%@ Page Title="升级VIP会员" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Upgrade.aspx.cs" Inherits="Account.Web.Upgrade" %>
<%@ Import Namespace="CommonLib" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="System.Linq" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
<meta name="description" content="升级OCR文字识别助手VIP会员，享受更多专业功能，个人版、专业版、旗舰版、运维版、技术版多种选择">
<style>
body {
    min-height: 100vh;
}

.upgrade-container{
    max-width:1200px;
    margin:0 auto;
    padding:20px;
    padding-top:65px;
    min-height:calc(100vh - 100px);
    position: relative;
}
.main-content{display:flex;gap:30px;align-items:flex-start}
.left-section{
    flex:2;
    background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);
    border-radius:16px;
    padding:30px;
    box-shadow:0 8px 32px rgba(0,0,0,0.12);
    border:1px solid rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
}
.right-section{
    flex:1;
    background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);
    border-radius:16px;
    padding:30px;
    box-shadow:0 8px 32px rgba(0,0,0,0.12);
    border:1px solid rgba(255,255,255,0.9);
    position:sticky;
    top:20px;
    backdrop-filter: blur(10px);
}
.section-title{font-size:1.3rem;font-weight:bold;color:#333;margin-bottom:20px;display:flex;align-items:center;justify-content:space-between}
.version-tabs{display:flex;gap:15px;margin-bottom:30px;flex-wrap:wrap}
.version-tab{flex:1;min-width:120px;padding:16px 16px 20px;border:2px solid #e9ecef;border-radius:12px;text-align:center;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;overflow:hidden}
.version-tab:hover{transform:translateY(-3px);box-shadow:0 6px 20px rgba(0,0,0,0.12)}
.version-tab.active{transform:translateY(-3px);box-shadow:0 8px 24px rgba(0,0,0,0.18);border-width:3px}
.version-tab.personal{border-color:#6666FF;backdrop-filter:blur(10px)}
.version-tab.personal:hover{border-color:#6666FF;background:rgba(102,102,255,0.08);box-shadow:0 6px 20px rgba(102,102,255,0.25)}
.version-tab.professional{border-color:#4B4B4B;backdrop-filter:blur(10px)}
.version-tab.professional:hover{border-color:#4B4B4B;background:rgba(75,75,75,0.08);box-shadow:0 6px 20px rgba(75,75,75,0.25)}
.version-tab.flagship{border-color:#E6D700;backdrop-filter:blur(10px)}
.version-tab.flagship:hover{border-color:#E6D700;background:rgba(255,235,193,0.15);box-shadow:0 6px 20px rgba(255,215,0,0.25)}
.version-recommend-badge{position:absolute;top:-2px;right:-2px;background:#ff4757;color:white;padding:4px 10px;border-radius:0 12px 0 12px;font-size:11px;font-weight:600;z-index:2;box-shadow:0 2px 6px rgba(255,71,87,0.4);letter-spacing:0.5px}
.version-audience{font-size:0.8rem;color:#666;margin-top:4px;font-style:italic}
.version-tab.personal.active{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border-color:#6666FF;color:white;box-shadow:0 4px 12px rgba(102,102,255,0.3)}
/* 智能推荐区域样式 - 增强版 */
.smart-recommendation{
    background:linear-gradient(135deg,rgba(0,123,250,0.08) 0%,rgba(0,123,250,0.12) 100%);
    border-radius:16px;
    padding:20px;
    margin-bottom:24px;
    text-align:center;
    border:2px solid rgba(0,123,250,0.2);
    position:relative;
    overflow:hidden;
    transition:all 0.3s ease;
    box-shadow:0 4px 16px rgba(0,123,250,0.1);
}

.smart-recommendation::before {
    content:'';
    position:absolute;
    top:0;
    left:-100%;
    width:100%;
    height:100%;
    background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);
    animation:shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left:-100%; }
    100% { left:100%; }
}

.smart-recommendation:hover {
    transform:translateY(-2px);
    box-shadow:0 8px 24px rgba(0,123,250,0.2);
    border-color:rgba(0,123,250,0.3);
}

.recommendation-trigger {
    display:flex;
    align-items:center;
    justify-content:center;
    gap:12px;
    flex-wrap:wrap;
}

.recommendation-trigger .fa-magic {
    animation:magicPulse 2s ease-in-out infinite;
}

@keyframes magicPulse {
    0%, 100% {
        transform:scale(1) rotate(0deg);
        color:#007cfa;
    }
    50% {
        transform:scale(1.1) rotate(5deg);
        color:#0056b3;
    }
}

.version-features-section.personal{background:transparent;border:1px solid rgba(102,102,255,0.03)}
.version-features-section.personal .features-header{background:transparent;border-bottom:none}
.version-features-section.personal .features-title{color:#4444BB;font-weight:600;font-size:17px}
.version-features-section.personal .features-compare-link{color:#6666FF}
.version-features-section.personal .features-compare-link:hover{color:#5555EE}
.version-features-section.personal .features-body{background:transparent}
.version-features-section.personal .feature-item:hover{background:rgba(102,102,255,0.04)}
.version-features-section.personal .feature-item i{color:#6666FF}
.version-features-section.personal .features-footer{background:transparent;border-top:none}
.version-features-section.personal .expand-btn{border-color:#6666FF;color:#6666FF;background:rgba(255,255,255,0.9)}
.version-features-section.personal .expand-btn:hover{border-color:#5555EE;color:#5555EE;background:rgba(255,255,255,1)}
.pricing-item.selected.personal{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border:none;color:white;box-shadow:0 2px 8px rgba(102,102,255,0.25)}
.pricing-item.selected.personal .pricing-name{color:white}
.pricing-item.selected.personal .current-price{color:white}
.pricing-item.selected.personal .original-price{color:rgba(255,255,255,0.8)}
.pricing-item.selected.personal .discount-percentage{color:white;background:rgba(255,255,255,0.2)}
.pricing-item.selected.personal .daily-cost{color:rgba(255,255,255,0.9)}
.pricing-item.personal:hover:not(.selected){border-color:#6666FF;background:linear-gradient(135deg,rgba(102,102,255,0.08) 0%,rgba(102,102,255,0.12) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(102,102,255,0.2)}
.btn-upgrade.personal{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border:none;color:white;box-shadow:0 2px 8px rgba(102,102,255,0.3)}
.btn-upgrade.personal:hover{background:linear-gradient(89.95deg,#5555EE 11.5%,#2AAFEE 100.01%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(102,102,255,0.4)}

.version-tab.professional.active{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border-color:#4B4B4B;color:#F9D9A8;box-shadow:0 4px 12px rgba(75,75,75,0.3)}
.version-features-section.professional{background:transparent;border:1px solid rgba(75,75,75,0.03)}
.version-features-section.professional .features-header{background:transparent;border-bottom:none}
.version-features-section.professional .features-title{color:#444444;font-weight:600;font-size:17px}
.version-features-section.professional .features-compare-link{color:#4B4B4B}
.version-features-section.professional .features-compare-link:hover{color:#3A3A3A}
.version-features-section.professional .features-body{background:transparent}
.version-features-section.professional .feature-item:hover{background:rgba(75,75,75,0.04)}
.version-features-section.professional .feature-item i{color:#4B4B4B}
.version-features-section.professional .features-footer{background:transparent;border-top:none}
.version-features-section.professional .expand-btn{border-color:#4B4B4B;color:#4B4B4B;background:rgba(255,255,255,0.9)}
.version-features-section.professional .expand-btn:hover{border-color:#3A3A3A;color:#3A3A3A;background:rgba(255,255,255,1)}
.pricing-item.selected.professional{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border:none;color:#F9D9A8;box-shadow:0 2px 8px rgba(75,75,75,0.3)}
.pricing-item.selected.professional .pricing-name{color:#F9D9A8}
.pricing-item.selected.professional .current-price{color:#F9D9A8}
.pricing-item.selected.professional .original-price{color:rgba(249,217,168,0.7)}
.pricing-item.selected.professional .discount-percentage{color:#F9D9A8;background:rgba(249,217,168,0.2)}
.pricing-item.selected.professional .daily-cost{color:rgba(249,217,168,0.9)}
.pricing-item.professional:hover:not(.selected){border-color:#4B4B4B;background:linear-gradient(135deg,rgba(75,75,75,0.08) 0%,rgba(75,75,75,0.12) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(75,75,75,0.2)}
.btn-upgrade.professional{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border:none;color:#F9D9A8;box-shadow:0 2px 8px rgba(75,75,75,0.3)}
.btn-upgrade.professional:hover{background:linear-gradient(to right,#3A3A3A 5.77%,#0F0A06 100%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(75,75,75,0.4)}

.version-tab.flagship.active{background:linear-gradient(135deg,#FFF8E7 0%,#FFE4B5 50%,#FFDB9A 100%);border-color:#E6D700;color:#944800;box-shadow:0 4px 12px rgba(255,215,0,0.3);position:relative}
.version-tab.flagship.active::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,0.3) 0%,transparent 50%);border-radius:6px;pointer-events:none}
.version-features-section.flagship{background:transparent;border:1px solid rgba(230,215,0,0.03)}
.version-features-section.flagship .features-header{background:transparent;border-bottom:none}
.version-features-section.flagship .features-title{color:#886622;font-weight:600;font-size:17px}
.version-features-section.flagship .features-compare-link{color:#944800}
.version-features-section.flagship .features-compare-link:hover{color:#7A3600}
.version-features-section.flagship .features-body{background:transparent}
.version-features-section.flagship .feature-item:hover{background:rgba(230,215,0,0.04)}
.version-features-section.flagship .feature-item i{color:#E6D700}
.version-features-section.flagship .features-footer{background:transparent;border-top:none}
.version-features-section.flagship .expand-btn{border-color:#944800;color:#944800;background:rgba(255,255,255,0.9)}
.version-features-section.flagship .expand-btn:hover{border-color:#7A3600;color:#7A3600;background:rgba(255,255,255,1)}
.pricing-item.selected.flagship{background:linear-gradient(135deg,#FFF8E7 0%,#FFE4B5 50%,#FFDB9A 100%);border:none;color:#944800;box-shadow:0 2px 8px rgba(255,215,0,0.3)}
.pricing-item.selected.flagship .pricing-name{color:#944800}
.pricing-item.selected.flagship .current-price{color:#944800}
.pricing-item.selected.flagship .original-price{color:rgba(148,72,0,0.7)}
.pricing-item.selected.flagship .discount-percentage{color:#944800;background:rgba(148,72,0,0.15)}
.pricing-item.selected.flagship .daily-cost{color:rgba(148,72,0,0.8)}
.pricing-item.flagship:hover:not(.selected){border-color:#E6D700;background:linear-gradient(135deg,rgba(255,235,193,0.12) 0%,rgba(255,215,0,0.08) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(255,215,0,0.2)}
.btn-upgrade.flagship{background:linear-gradient(135deg,#FFF8E7 0%,#FFE4B5 50%,#FFDB9A 100%);border:none;color:#944800;font-weight:bold;box-shadow:0 2px 8px rgba(255,215,0,0.3)}
.btn-upgrade.flagship:hover{background:linear-gradient(135deg,#FFE4B5 0%,#FFDB9A 50%,#FFD700 100%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(255,215,0,0.4)}

.btn-upgrade{transition:all 0.3s ease;border-radius:6px;padding:12px 24px;font-size:16px;font-weight:500;position:relative;overflow:hidden}
.btn-upgrade::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:left 0.5s;z-index:1}
.btn-upgrade:hover::before{left:100%}
.version-tab{animation:fadeIn 0.4s ease-out}
.version-tab:nth-child(1){animation-delay:0.05s}
.version-tab:nth-child(2){animation-delay:0.1s}
.version-tab:nth-child(3){animation-delay:0.15s}
.trust-elements{animation:fadeIn 0.8s ease-out 0.5s both}
@keyframes fadeIn{from{opacity:0}to{opacity:1}}

/* 新增推荐结果动画 */
@keyframes celebrationPulse {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.05); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes fadeOutUp {
    0% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
}

@keyframes compactScale {
    0% { transform: scale(1); }
    100% { transform: scale(0.85); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-15px); }
    70% { transform: translateY(-7px); }
    90% { transform: translateY(-3px); }
}

@keyframes scaleIn {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes slideInUp {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
    0% { transform: translateX(-30px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    0% { transform: translateX(30px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes progressFill {
    0% { stroke-dashoffset: 314; }
    100% { stroke-dashoffset: var(--target-offset); }
}

@keyframes countUp {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* 推荐结果移动端适配 */
@media (max-width: 768px) {
    .recommendation-result {
        padding: 16px !important;
    }

    .celebration-header div:first-child {
        font-size: 48px !important;
    }

    .recommended-version-card {
        padding: 20px !important;
    }

    .match-percentage svg {
        width: 100px !important;
        height: 100px !important;
    }

    .recommendation-reasons .grid,
    .user-profile .grid,
    .version-comparison .grid {
        gap: 12px !important;
    }

    .action-section .flex {
        flex-direction: column !important;
        gap: 12px !important;
    }
}
.feature-item{transition:all 0.2s ease}
.feature-item:hover{transform:translateX(4px)}
.security-badges .badge-item:hover{transform:translateY(-1px);box-shadow:0 2px 8px rgba(40,167,69,0.2)}
.pricing-item{
    height:120px;
    padding:16px 20px;
    border:2px solid #e9ecef;
    border-radius:16px;
    text-align:center;
    cursor:pointer;
    transition:all 0.3s ease;
    background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);
    margin:0 0 12px 0;
    position:relative;
    overflow:hidden;
    display:flex;
    flex-direction:column;
    justify-content:center;
    box-shadow:0 6px 20px rgba(0,0,0,0.1);
    backdrop-filter: blur(3px);
}
.pricing-name{font-size:1rem;font-weight:700;margin-bottom:8px;color:#333}
.pricing-price{display:flex;align-items:center;justify-content:center;gap:8px;margin-bottom:6px;flex-wrap:wrap}
.current-price{font-size:1.5rem;font-weight:bold;color:#333;position:relative}
.original-price{font-size:1rem;color:#999;text-decoration:line-through;opacity:0.8}
.discount-percentage{font-size:0.85rem;color:#ff4757;font-weight:600;background:rgba(255,71,87,0.1);padding:2px 6px;border-radius:12px;margin-left:4px}
.daily-cost{font-size:0.75rem;color:#666;margin-top:2px;font-style:italic}
.pricing-item.selected{box-shadow:0 8px 24px rgba(0,0,0,0.15);border:none}
.pricing-tag{position:absolute;top:-2px;left:12px;padding:4px 12px;border-radius:0 0 12px 12px;font-size:11px;font-weight:bold;z-index:3;color:white;text-shadow:0 1px 2px rgba(0,0,0,0.2)}
.pricing-tag.hot{background:linear-gradient(135deg,#ff6b35 0%,#ff4757 100%);box-shadow:0 3px 8px rgba(255,71,87,0.4)}
.pricing-tag.recommend{background:linear-gradient(135deg,#ff6348 0%,#e55039 100%);box-shadow:0 3px 8px rgba(229,80,57,0.4)}
.pricing-tag.new{background:linear-gradient(135deg,#2ed573 0%,#1dd1a1 100%);box-shadow:0 3px 8px rgba(29,209,161,0.4)}
.discount-badge{position:absolute;top:-2px;right:-2px;background:linear-gradient(135deg,#ff4757 0%,#ff3742 100%);color:white;padding:6px 12px;border-radius:0 12px 0 16px;font-size:13px;font-weight:bold;z-index:2;box-shadow:0 3px 8px rgba(255,71,87,0.4);text-shadow:0 1px 2px rgba(0,0,0,0.2)}
.version-tab .tab-icon{width:32px;height:32px;margin:0 auto 8px;background-size:contain;background-repeat:no-repeat;background-position:center}
.version-tab .tab-name{font-weight:bold;font-size:1rem}

.pricing-section{margin-bottom:30px}
.pricing-options{display:none}
.pricing-options.active{display:grid;grid-template-columns:repeat(3,1fr);gap:16px}
.version-features-section{
    margin:20px 0;
    border-radius:12px;
    transition:border-color 0.3s ease, box-shadow 0.3s ease;
    background:linear-gradient(135deg,rgba(255,255,255,0.8) 0%,rgba(250,251,252,0.8) 100%);
    border:1px solid rgba(200,200,200,0.3);
    box-shadow:0 4px 16px rgba(0,0,0,0.06);
    overflow:hidden;
    backdrop-filter: blur(3px);
}
.features-header{display:flex;justify-content:space-between;align-items:center;padding:16px 16px 12px;margin:0;border-bottom:none;background:transparent}
.features-title{font-size:16px;font-weight:600;margin:0;color:#333}
.features-compare-link{color:#007cfa;text-decoration:none;font-size:14px;transition:color 0.3s ease;font-weight:400;cursor:pointer}
.features-compare-link:hover{color:#0056b3;text-decoration:none}

.compare-modal{display:none;position:fixed;z-index:1000;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5)}
.compare-modal-content{background-color:#fefefe;margin:1% auto;padding:0;border-radius:16px;width:90%;max-width:1400px;height:95vh;overflow:hidden;box-shadow:0 12px 48px rgba(0,0,0,0.15);display:flex;flex-direction:column}
.compare-modal-header{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);padding:14px 32px;border-bottom:1px solid #dee2e6;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;border-radius:16px 16px 0 0}
.compare-modal-title{font-size:22px;font-weight:600;color:#2c3e50;margin:0;display:flex;align-items:center}
.compare-modal-title::before{content:"📊";margin-right:8px;font-size:20px}
.compare-modal-close{color:#6c757d;font-size:32px;font-weight:bold;cursor:pointer;line-height:1;padding:8px;border-radius:50%;transition:all 0.3s ease;display:flex;align-items:center;justify-content:center;width:48px;height:48px}
.compare-modal-close:hover{color:#dc3545;background-color:rgba(220,53,69,0.1);transform:scale(1.1)}
.compare-modal-body{padding:0;flex:1;overflow:hidden;border-radius:0 0 16px 16px}
.compare-modal-body iframe{width:100%;height:100%;border:none;border-radius:0 0 16px 16px}

@media (max-width:1200px){.compare-modal-content{width:95%;max-width:none}}
@media (max-width:768px){.compare-modal-content{width:98%;height:98vh;margin:1% auto;border-radius:12px}.compare-modal-header{padding:16px 20px;border-radius:12px 12px 0 0}.compare-modal-title{font-size:18px}.compare-modal-close{width:40px;height:40px;font-size:28px}.compare-modal-body iframe{border-radius:0 0 12px 12px}}

/* 问卷弹窗滚动条样式 */
.quiz-body::-webkit-scrollbar{width:6px}
.quiz-body::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}
.quiz-body::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}
.quiz-body::-webkit-scrollbar-thumb:hover{background:#a8a8a8}

/* 问卷弹窗滚动条样式 */
.quiz-body::-webkit-scrollbar{width:6px}
.quiz-body::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}
.quiz-body::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}
.quiz-body::-webkit-scrollbar-thumb:hover{background:#a8a8a8}

/* 使用场景标签样式 */
.scenario-tags{
    display:flex;
    flex-wrap:wrap;
    gap:6px;
}
.scenario-tag{
    background:rgba(0,123,250,0.1);
    color:#007cfa;
    padding:4px 8px;
    border-radius:12px;
    font-size:11px;
    border:1px solid rgba(0,123,250,0.2);
}
.features-body{padding:8px 16px 12px;background:transparent}
.features-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:3px 8px;margin:0}
.feature-item{display:flex;align-items:flex-start;padding:6px 0;background:transparent;border:none;font-size:14px;transition:background-color 0.2s ease}
.feature-item:hover{background:rgba(255,255,255,0.4);border-radius:4px}
.feature-item i{color:#28a745;margin-right:6px;font-size:14px;margin-top:1px;flex-shrink:0}
.feature-item .feature-content{flex:1}
.feature-item .feature-name{font-weight:500;color:#333;margin-bottom:1px;line-height:1.2;font-size:14px}
.feature-item .feature-desc{color:#666;font-size:12px;line-height:1.2}

.features-footer{padding:8px 16px 16px;background:transparent;border-top:none;text-align:center}
.expand-btn{background:rgba(255,255,255,0.9);border:1px solid #007cfa;color:#007cfa;font-size:13px;cursor:pointer;display:inline-flex;align-items:center;justify-content:center;transition:all 0.2s ease;padding:6px 14px;border-radius:14px;font-weight:400;box-shadow:0 1px 3px rgba(0,0,0,0.08)}
.expand-btn:hover{color:#0056b3;background:rgba(255,255,255,1);border-color:#0056b3;box-shadow:0 2px 6px rgba(0,0,0,0.12);transform:translateY(-1px)}
.expand-btn i{margin-left:4px;transition:transform 0.3s ease;font-size:12px}
.expand-btn.expanded i{transform:rotate(180deg)}

.order-summary{
    border:1px solid rgba(0,123,250,0.15);
    border-radius:16px;
    padding:24px;
    margin-bottom:24px;
    background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);
    box-shadow:0 6px 24px rgba(0,0,0,0.08);
    backdrop-filter: blur(5px);
}
.summary-item{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding:8px 0}
.summary-label{color:#666;font-size:16px;font-weight:600}
.summary-value{font-weight:600;color:#333;font-size:15px}
.summary-item.discount .summary-value{color:#ff4757}
.summary-divider{height:2px;background:linear-gradient(90deg,transparent,#e9ecef,transparent);margin:20px 0}
.summary-total{display:flex;justify-content:space-between;align-items:center;margin-top:20px;padding-top:20px;border-top:2px solid #007cfa}
.total-label{font-size:1.1rem;font-weight:bold;color:#333}
.total-price{font-size:1.5rem;font-weight:bold;color:#007cfa}
.order-total{padding:20px 0;border-top:2px solid rgba(0,123,250,0.1);background:linear-gradient(135deg,rgba(0,123,250,0.02) 0%,rgba(0,123,250,0.05) 100%);border-radius:12px;margin:-8px -8px 0 -8px;padding:20px 16px}
.total-row{display:flex;justify-content:space-between;align-items:center}
.total-prices{text-align:right;display:flex;flex-direction:column;align-items:flex-end;gap:4px}
.discount-info{color:#ff6600;font-size:13px;font-weight:600;background:linear-gradient(135deg,rgba(255,102,0,0.1) 0%,rgba(255,102,0,0.15) 100%);padding:4px 10px;border-radius:16px;border:1px solid rgba(255,102,0,0.2);animation:pulse-discount 2s infinite}
@keyframes pulse-discount{0%,100%{transform:scale(1)}50%{transform:scale(1.02)}}
@keyframes pulse{0%,100%{opacity:1}50%{opacity:0.6}}
.urgency-notice{animation:glow 3s ease-in-out infinite alternate}
@keyframes glow{from{box-shadow:0 0 5px rgba(255,102,0,0.3)}to{box-shadow:0 0 15px rgba(255,102,0,0.5)}}
.original-total{color:#999;font-size:18px;text-decoration:line-through;opacity:0.8;font-weight:600}
.current-total{color:#007cfa;font-size:40px;font-weight:900;text-shadow:0 2px 6px rgba(0,123,250,0.3);line-height:1;animation:price-pulse 3s ease-in-out infinite}
@keyframes price-pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.02)}}
.trust-elements{
    margin-bottom:30px;
    padding:24px;
    background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);
    border-radius:16px;
    border:1px solid rgba(0,123,250,0.12);
    box-shadow:0 6px 24px rgba(0,0,0,0.08);
    backdrop-filter: blur(5px);
}
.social-proof{margin-bottom:20px;text-align:center}
.user-stats{display:flex;align-items:center;justify-content:center;margin-bottom:12px;font-size:15px;color:#333;font-weight:500}
.user-stats i{color:#007cfa;margin-right:10px;font-size:18px;animation:pulse 2s infinite}
.user-stats strong{color:#007cfa;font-size:16px;font-weight:700}
.user-count{display:inline-block;min-width:60px;text-align:center}
.rating-info{display:flex;align-items:center;justify-content:center;gap:10px;font-size:14px;color:#666}
.stars{display:flex;gap:3px}
.stars i{color:#ffc107;font-size:16px;transition:transform 0.2s ease}
.stars i:hover{transform:scale(1.1)}
.rating-text{font-weight:500}
.security-badges{display:grid;grid-template-columns:1fr;gap:10px;margin-top:16px}
.badge-item{display:flex;align-items:center;padding:12px 16px;background:linear-gradient(135deg,rgba(40,167,69,0.08) 0%,rgba(40,167,69,0.12) 100%);border-radius:12px;font-size:13px;color:#28a745;border:1px solid rgba(40,167,69,0.15);transition:all 0.3s ease;font-weight:500}
.badge-item:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(40,167,69,0.2);background:linear-gradient(135deg,rgba(40,167,69,0.12) 0%,rgba(40,167,69,0.16) 100%)}
.badge-item i{margin-right:8px;font-size:16px}
@keyframes shine{0%{left:-100%}100%{left:100%}}
.right-section{animation:slideInRight 0.6s ease-out}
.order-summary{animation:fadeInUp 0.8s ease-out 0.2s both}
.summary-item{animation:fadeIn 0.6s ease-out both}
.summary-item:nth-child(1){animation-delay:0.1s}
.summary-item:nth-child(2){animation-delay:0.2s}
.summary-item:nth-child(3){animation-delay:0.3s}
.order-total{animation:fadeIn 0.6s ease-out 0.4s both}
.account-input{margin:24px 0}
.account-input .form-control{width:100%;padding:16px;border:2px solid #e9ecef;border-radius:12px;font-size:15px;transition:all 0.3s ease;background:rgba(255,255,255,0.8)}
.account-input .form-control:focus{border-color:#007cfa;box-shadow:0 0 0 3px rgba(0,123,250,0.1);outline:none;background:white}
.account-input .form-control::placeholder{color:#999;font-weight:400}
.btn-upgrade{width:100%;padding:18px;background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;border:none;border-radius:12px;font-size:16px;font-weight:600;cursor:pointer;transition:all 0.3s ease;position:relative;overflow:hidden;box-shadow:0 4px 16px rgba(0,123,250,0.3);text-transform:uppercase;letter-spacing:0.5px}
.btn-upgrade:hover{background:linear-gradient(135deg,#0056b3 0%,#004494 100%);transform:translateY(-2px);box-shadow:0 6px 20px rgba(0,123,250,0.4)}
.btn-upgrade:active{transform:translateY(0);box-shadow:0 2px 8px rgba(0,123,250,0.3)}
.btn-upgrade::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:left 0.5s;z-index:1}
.btn-upgrade:hover::before{left:100%}
.btn-upgrade.loading{pointer-events:none;opacity:0.8}
.btn-upgrade.loading::after{content:'';position:absolute;top:50%;left:50%;width:20px;height:20px;margin:-10px 0 0 -10px;border:2px solid transparent;border-top:2px solid white;border-radius:50%;animation:spin 1s linear infinite;z-index:2}
@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
.icon-v-1{background:url(/static/image/vip_1.png) 0 center no-repeat;background-size:contain}
.icon-v1{background:url(/static/image/vip_2.png) 0 center no-repeat;background-size:contain}
.icon-v3{background:url(/static/image/vip_3.png) 0 center no-repeat;background-size:contain}
@media (max-width:768px){
.upgrade-container{padding:15px 10px;padding-top:60px}
.main-content{flex-direction:column;gap:20px}
.left-section,.right-section{
    padding:20px 16px;
    border-radius:12px;
    box-shadow:0 6px 24px rgba(0,0,0,0.1);
}
.right-section{position:static;margin-top:20px}
.version-tabs{flex-direction:column;gap:12px}
.version-tab{min-width:auto;padding:12px 16px 16px;margin-bottom:0}
.version-tab .tab-icon{width:28px;height:28px;margin-bottom:6px}
.version-tab .tab-name{font-size:0.95rem}
.version-tab .version-audience{font-size:0.75rem}
.version-recommend-badge{padding:3px 8px;font-size:10px}
.pricing-options{grid-template-columns:1fr;gap:12px}
.pricing-item{height:auto;min-height:100px;padding:14px 16px}
.pricing-name{font-size:0.95rem}
.current-price{font-size:1.3rem}
.original-price{font-size:0.9rem}
.discount-percentage{font-size:0.8rem}
.daily-cost{font-size:0.7rem}
.discount-badge{padding:4px 8px;font-size:11px}
.features-grid{grid-template-columns:1fr;gap:6px}
.version-features-section{margin:15px 0;border-radius:8px}
.features-header{padding:12px 16px}
.features-body{padding:8px 16px 12px}
.features-footer{padding:8px 16px 12px}
.feature-item{padding:8px 0;font-size:14px}
.feature-item .feature-name{font-size:14px}
.feature-item .feature-desc{font-size:12px}
.expand-btn{padding:8px 16px;font-size:13px}
.trust-elements{padding:20px;margin-bottom:24px;border-radius:12px}
.user-stats{font-size:14px}
.user-stats i{font-size:16px}
.rating-info{font-size:13px}
.stars i{font-size:15px}
.security-badges{grid-template-columns:1fr;gap:8px}
.badge-item{padding:10px 12px;font-size:12px}
.badge-item i{font-size:14px}

.order-summary{padding:20px;border-radius:12px}
.summary-item{margin-bottom:14px}
.summary-label{font-size:15px}
.summary-value{font-size:14px}
.order-total{padding:16px 12px;margin:-6px -6px 0 -6px}
.current-total{font-size:20px}
.discount-info{font-size:12px}
.original-total{font-size:13px}
.account-input .form-control{padding:14px;font-size:14px}
.btn-upgrade{padding:16px;font-size:15px}
.user-testimonials{height:58px}
.testimonial-item{height:50px;padding:10px;margin-bottom:6px}
.testimonial-item div:first-child{font-size:12px}
.testimonial-item div:last-child{font-size:10px}
.security-badges div{font-size:11px;padding:4px 8px;margin:2px}
.service-guarantee{margin:12px 0;padding:10px 12px}
.service-guarantee div:first-child{font-size:13px;margin-bottom:6px}
.service-guarantee div:last-child div{font-size:11px;padding:3px 6px}
.account-section{min-height:50px}
.account-info{padding:12px;font-size:12px}
.payment-methods{margin:12px 0}
.payment-methods div:first-child{font-size:11px}
.payment-methods i{font-size:20px}
.help-links{margin-top:12px;font-size:11px}
.current-total{font-size:36px}
.original-total{font-size:12px}
.discount-info{font-size:10px;padding:1px 6px}
}
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upgrade-container">
        <div class="main-content">
            <div class="left-section">
                <div class="smart-recommendation">
                    <div class="recommendation-trigger">
                        <i class="fa fa-magic" style="color: #007cfa; margin-right: 8px;"></i>
                        <span style="color: #666; margin-right: 12px;">不确定选哪个版本？</span>
                        <button type="button" class="quiz-btn" onclick="openSmartRecommendationIframe(); return false;" style="background: linear-gradient(135deg, #007cfa 0%, #0056b3 100%); color: white; border: none; padding: 8px 16px; border-radius: 20px; font-size: 13px; cursor: pointer; transition: all 0.3s ease;">
                            <i class="fa fa-bolt" style="margin-right: 4px;"></i>30秒找到最适合的方案
                        </button>
                    </div>
                </div>
                <div class="section-title">
                    选择升级类型
                </div>

                <div class="version-tabs" id="versionTabs">
                    <%
                        var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();

                        // 检查用户登录状态
                        var currentUser = Account.Web.Common.AuthHelper.GetUserSession(Request);
                        var isLoggedIn = currentUser != null && !string.IsNullOrEmpty(currentUser.Account);
                        var currentAccount = isLoggedIn ? currentUser.Account : "";

                        int tabIndex = 0;
                        foreach (var userType in lstUserTypes)
                        {
                            var typeHash = userType.Type.GetHashCode();
                            var isActive = tabIndex == 0 ? "active" : "";
                            var iconClass = "icon-v" + typeHash;

                            // 根据版本类型设置描述、CSS类、推荐标签和适用人群
                            string typeDesc = "";
                            string versionClass = "";
                            string recommendBadge = "";
                            string targetAudience = "";

                            switch (userType.Type.ToString())
                            {
                                case "个人版":
                                    typeDesc = "个人专属，轻松识别";
                                    versionClass = "personal";
                                    recommendBadge = "最多选择";
                                    targetAudience = "";
                                    break;
                                case "专业版":
                                    typeDesc = "专业高效，精确稳定";
                                    versionClass = "professional";
                                    recommendBadge = "推荐";
                                    targetAudience = "";
                                    break;
                                case "旗舰版":
                                    typeDesc = "旗舰体验，最佳选择";
                                    versionClass = "flagship";
                                    recommendBadge = "热门";
                                    targetAudience = "";
                                    break;
                                default:
                                    typeDesc = "专业服务，品质保证";
                                    versionClass = "professional";
                                    recommendBadge = "";
                                    targetAudience = "";
                                    break;
                            }
                    %>
                    <div class="version-tab <%=versionClass %> <%=isActive %>" data-type="<%=typeHash %>" data-desc="<%=typeDesc %>" onclick="selectVersion('<%=typeHash %>')">
                        <%if (!string.IsNullOrEmpty(recommendBadge)) { %>
                        <div class="version-recommend-badge"><%=recommendBadge %></div>
                        <%} %>
                        <div class="tab-icon <%=iconClass %>"></div>
                        <div class="tab-name"><%=userType.Type.ToString() %></div>
                    </div>
                    <%
                            tabIndex++;
                        }
                    %>
                </div>

                <!-- 版本专属功能区域 -->
                <div class="version-features-section" id="versionFeaturesSection">
                    <!-- 标题栏 -->
                    <div class="features-header">
                        <h3 class="features-title" id="featuresTitle">专属功能</h3>
                        <a href="javascript:void(0);" class="features-compare-link" onclick="openCompareModal()"><i class="fa fa-info-circle"></i>&nbsp;功能比对 ></a>
                    </div>

                    <!-- 内容区域 -->
                    <div class="features-body">
                        <div class="features-grid" id="featuresGrid">
                            <!-- 功能项将通过JavaScript动态生成 -->
                        </div>
                        <div class="use-cases" id="useCases" style="margin-top: 16px; padding-top: 16px; border-top: 1px solid rgba(0,0,0,0.1);">
                            <h4 style="font-size: 14px; color: #666; margin-bottom: 8px; font-weight: 600;">
                                <i class="fa fa-lightbulb" style="margin-right: 6px; color: #ffc107;"></i>适用场景
                            </h4>
                            <div class="scenario-tags" id="scenarioTags"><span class="scenario-tag">扫描文档</span><span class="scenario-tag">提取图片文字</span><span class="scenario-tag">学习笔记整理</span><span class="scenario-tag">日常办公</span></div>
                        </div>
                    </div>

                    <!-- 底部区域 -->
                    <div class="features-footer" id="featuresExpand" style="display: none;">
                        <button type="button" class="expand-btn" onclick="return toggleFeatures(event);">
                            <span id="expandText">展开</span>
                            <i class="fa fa-angle-down" id="expandIcon"></i>
                        </button>
                    </div>
                </div>



                <div class="section-title">选择订阅方式</div>
                
                <div class="pricing-section" id="pricingSection">
                    <%
                        tabIndex = 0;
                        foreach (var userType in lstUserTypes)
                        {
                            var typeHash = userType.Type.GetHashCode();
                            var isActive = tabIndex == 0 ? "active" : "";

                            // 构建价格选项列表
                            var lstChargeType = new List<ChargeViewToUser>();
                            try
                            {
                                if (userType.ChargeTypes != null)
                                {
                                    foreach (var q in userType.ChargeTypes)
                                    {
                                        string strDesc = "";
                                        var price = q.GetPrice(userType.PerPrice, ref strDesc);
                                        var charge = new ChargeViewToUser()
                                        {
                                            Name = q.Name,
                                            Desc = strDesc,
                                            Price = (double)price,
                                            OriPrice = (double)q.OriPrice,
                                            IsDefault = q.IsDefault,
                                            Tag = q.Tag,
                                        };
                                        lstChargeType.Add(charge);
                                    }
                                }
                            }
                            catch
                            {
                                // 如果价格计算失败，使用默认价格
                                lstChargeType.Add(new ChargeViewToUser
                                {
                                    Name = "一年",
                                    Desc = "",
                                    Price = 100,
                                    OriPrice = 100,
                                    IsDefault = true,
                                    Tag = ""
                                });
                            }
                    %>
                    <div class="pricing-options <%=isActive %>" data-type="<%=typeHash %>">
                        <%
                            int priceIndex = 0;
                            foreach (var charge in lstChargeType)
                            {
                                var isSelected = charge.IsDefault ? "selected" : "";
                                var tagClass = "";
                                var tagText = "";

                                switch (charge.Tag != null ? charge.Tag.ToLower() : null)
                                {
                                    case "hot":
                                        tagClass = "hot";
                                        tagText = "热门";
                                        break;
                                    case "_new":
                                        tagClass = "new";
                                        tagText = "新";
                                        break;
                                    case "recommond":
                                        tagClass = "recommend";
                                        tagText = "推荐";
                                        break;
                                }

                                // 计算折扣率（几折）
                                var discountRate = charge.OriPrice > 0 && charge.OriPrice > charge.Price ? Math.Round((charge.Price / charge.OriPrice) * 10, 1) : 0;
                        %>
                        <div class="pricing-item <%=isSelected %>" data-name="<%=charge.Name %>" data-price="<%=charge.Price %>" data-original="<%=charge.OriPrice %>" data-desc="<%=charge.Desc %>" data-is-default="<%=charge.IsDefault.ToString().ToLower() %>" onclick="selectPricing(this)">
                            <%if (!string.IsNullOrEmpty(tagText)) { %>
                            <div class="pricing-tag <%=tagClass %>"><%=tagText %></div>
                            <%} %>
                            <%if (charge.OriPrice > charge.Price) {
                                // 正确的折扣计算：现价/原价*10，保留一位小数
                                var discountPercent = Math.Round((charge.Price / charge.OriPrice) * 10, 1);
                            %>
                            <div class="discount-badge"><%=discountPercent %>折</div>
                            <%} %>
                            <div class="pricing-name"><%=charge.Name %></div>
                            <div class="pricing-price">
                                <span class="current-price">¥<%=charge.Price.ToString("F0") %></span>
                                <%if (charge.OriPrice > charge.Price) { %>
                                <span class="original-price">¥<%=charge.OriPrice.ToString("F0") %></span>
                                <span class="discount-percentage">省¥<%=(charge.OriPrice - charge.Price).ToString("F0") %></span>
                                <%} %>
                            </div>
                            <%
                                // 计算每日成本 - 通用时间单位解析
                                var dailyCost = 0.0;
                                var days = 365; // 默认一年

                                if (charge.Name.Contains("终身")) {
                                    days = 365 * 10; // 终身按10年计算
                                } else {
                                    var name = charge.Name;
                                    var chineseNumbers = "一二三四五六七八九十";
                                    var number = 1;

                                    // 解析数字（汉字或阿拉伯数字）
                                    var index = chineseNumbers.IndexOfAny(name.ToCharArray());
                                    if (index >= 0) {
                                        number = index + 1;
                                    } else {
                                        var match = System.Text.RegularExpressions.Regex.Match(name, @"(\d+)");
                                        if (match.Success) int.TryParse(match.Groups[1].Value, out number);
                                    }

                                    // 根据时间单位计算天数
                                    if (name.Contains("年")) {
                                        days = 365 * number;
                                    } else if (name.Contains("月")) {
                                        days = 30 * number;
                                    } else if (name.Contains("日") || name.Contains("天")) {
                                        days = number;
                                    }
                                }

                                dailyCost = charge.Price / days;
                            %>
                            <div class="daily-cost">每天仅需¥<%=dailyCost.ToString("F2") %></div>
                        </div>
                        <%
                                priceIndex++;
                            }
                        %>
                    </div>
                    <%
                            tabIndex++;
                        }
                    %>
                </div>
            </div>

            <div class="right-section">
                <!-- 信任要素区域 -->
                <div class="trust-elements">
                    <div class="social-proof">
                        <div class="user-stats">
                            <i class="fa fa-users"></i>
                            <span>已有 <strong><span class="user-count">50,000+</span></strong> 用户选择升级</span>
                        </div>
                        <div class="rating-info">
                            <div class="stars">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                            </div>
                            <span class="rating-text">4.8分 (2,341条评价)</span>
                        </div>

                        <div class="user-testimonials" style="margin-top:16px;height:68px;overflow:hidden;position:relative;">
                            <div class="testimonial-slider" id="testimonialSlider" style="transition:transform 0.5s ease;">
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "识别准确率很高，大大提升了工作效率！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自北京的李**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "批量处理文档太方便了，节省了大量时间！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自上海的王**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "客服响应很快，技术支持很专业！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自广州的张**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "表格识别功能太强大了，完全解放双手！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自深圳的陈**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "多设备同步使用，办公效率翻倍！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自杭州的刘**用户
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:16px;">
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-certificate" style="margin-right:4px;font-size:11px;"></i>正版授权
                        </div>
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-cloud-upload-alt" style="margin-right:4px;font-size:11px;"></i>服务稳定
                        </div>
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-users-cog" style="margin-right:4px;font-size:11px;"></i>专业团队
                        </div>
                    </div>
                </div>
                <%
                    // 定义默认值变量
                    var firstUserType = lstUserTypes.FirstOrDefault();
                    var defaultCharge = firstUserType != null && firstUserType.ChargeTypes != null ? firstUserType.ChargeTypes.FirstOrDefault(c => c.IsDefault) : null;
                    string defaultVersionName = firstUserType != null ? firstUserType.Type.ToString() : "";
                    string defaultPlanName = defaultCharge != null ? defaultCharge.Name : "一年";
                    string defaultDesc = defaultCharge != null ? defaultCharge.Desc : "";
                    decimal defaultOriPrice = defaultCharge != null ? defaultCharge.OriPrice : 100;
                    decimal defaultCurrentPrice = 100;
                    decimal defaultDiscount = 0;

                    if (defaultCharge != null && firstUserType != null)
                    {
                        string tempDesc = "";
                        defaultCurrentPrice = defaultCharge.GetPrice(firstUserType.PerPrice, ref tempDesc);
                        defaultDiscount = defaultOriPrice - defaultCurrentPrice;
                    }
                %>
                <div class="order-summary" id="orderSummary">
                    <div class="summary-item">
                        <div class="summary-label"><i class="fa fa-crown" style="margin-right:6px;color:#ffc107;"></i>升级方案</div>
                        <div class="summary-value" id="selectedPlan"><%=defaultPlanName + defaultVersionName %></div>
                    </div>

                    <div class="summary-item">
                        <div class="summary-label"><i class="fa fa-info-circle" style="margin-right:6px;color:#007cfa;"></i>服务说明</div>
                        <div class="summary-value" id="selectedDesc"><%=defaultDesc %></div>
                    </div>



                    <div class="summary-divider"></div>

                    <div class="order-total" style="background:linear-gradient(135deg,rgba(0,123,250,0.05) 0%,rgba(0,123,250,0.08) 100%);border:2px solid rgba(0,123,250,0.15);border-radius:16px;padding:10px 20px 10px 20px;margin:16px 0;position:relative;">
                        <div style="position:absolute;top:-8px;right:-2px;background:linear-gradient(135deg,#ff6600,#ff8533);color:white;padding:6px 12px;border-radius:12px;font-size:13px;font-weight:700;box-shadow:0 2px 8px rgba(255,102,0,0.3);border:1px solid rgba(255,255,255,0.2);" id="discountAmount">
                            <i class="fa fa-clock" style="margin-right:4px;animation:pulse 2s infinite;"></i>限时节省¥<%=(defaultOriPrice - defaultCurrentPrice).ToString("F0") %>
                        </div>

                        <div class="total-row" style="justify-content:center;">
                            <div class="total-prices">
                                <div style="display:flex;align-items:baseline;gap:16px;justify-content:center;">
                                    <span class="current-total" style="font-size:40px;font-weight:900;color:#007cfa;text-shadow:0 2px 6px rgba(0,123,250,0.3);line-height:1;">¥<%=defaultCurrentPrice.ToString("F0") %></span>
                                    <span class="original-total" style="font-size:18px;color:#999;text-decoration:line-through;opacity:0.8;font-weight:600;">¥<%=defaultOriPrice.ToString("F0") %></span>
                                </div>
                            </div>
                        </div>
                    </div>

                        <%if (!isLoggedIn) { %>
                    <div class="account-section" style="min-height:60px;margin:16px 0;">
                        <div class="account-input" id="accountInput">
                            <input type="text" id="txtAccount" placeholder="请输入您的账号（手机号/邮箱）" class="form-control" value="<%=currentAccount %>" />
                        </div>
                    </div>
                        <%} %>

                    <button type="button" class="btn-upgrade" id="upgradeBtn" onclick="submitUpgrade()" style="position:relative;overflow:hidden;margin:10px 0 12px 0;">
                        <span class="btn-text">立即升级</span>
                        <div class="btn-shine" style="position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);animation:shine 2s infinite;"></div>
                    </button>

                    <div class="service-guarantee" style="background:rgba(40,167,69,0.08);margin:10px 0;padding:16px;border-radius:12px;border-left:4px solid #28a745;">
                        <div style="color:#28a745;font-weight:600;font-size:14px;margin-bottom:12px;text-align:left;">
                            <i class="fa fa-shield-check" style="margin-right:6px;"></i>服务保障
                        </div>
                        <div style="display:flex;justify-content:center;flex-wrap:wrap;gap:8px;">
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-bolt" style="margin-right:4px;font-size:10px;"></i>立即生效
                            </div>
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-undo" style="margin-right:4px;font-size:10px;"></i>7天退款
                            </div>
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-headset" style="margin-right:4px;font-size:10px;"></i>24h客服
                            </div>
                        </div>
                    </div>


                </div>


            </div>
        </div>
    </div>

    <!-- 功能对比弹框 -->
    <div id="compareModal" class="compare-modal">
        <div class="compare-modal-content">
            <div class="compare-modal-header">
                <h2 class="compare-modal-title">各版本功能比较</h2>
                <span class="compare-modal-close" onclick="closeCompareModal()">&times;</span>
            </div>
            <div class="compare-modal-body">
                <iframe id="compareIframe" src="desc.aspx" frameborder="0"></iframe>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        var currentAccount = '';
        var phoneReg = /^1[3456789]\d{9}$/;
        var emailReg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;

// 版本核心功能数据
var versionCoreFeatures={'个人版':[{name:'基础识别套装',desc:'本地识别+图片识别+区域识别+竖排识别'},{name:'翻译功能',desc:'划词翻译+图片翻译'},{name:'使用限制',desc:'每日200次，1秒/次'},{name:'设备授权',desc:'最多2台设备同时使用'},{name:'专属客服',desc:'优先技术支持'},{name:'需求定制',desc:'个性化功能定制'}],'专业版':[{name:'基础识别套装',desc:'包含个人版全部识别功能'},{name:'高级识别',desc:'公式识别+表格识别'},{name:'翻译功能',desc:'划词翻译+图片翻译'},{name:'性能提升',desc:'每日500次，1秒/2次'},{name:'多设备支持',desc:'最多3台设备同时使用'},{name:'专属客服',desc:'优先技术支持+需求定制'}],'旗舰版':[{name:'完整识别套装',desc:'包含专业版全部功能'},{name:'文档处理',desc:'文档识别+文档翻译+批量识别'},{name:'多结果显示',desc:'多种识别结果对比'},{name:'极速体验',desc:'每日2000次，1秒/3次'},{name:'多设备授权',desc:'最多5台设备同时使用'},{name:'自选通道',desc:'多种识别引擎可选择'}]};

// 使用场景数据
var versionScenarios = {
    '个人版': ['扫描文档', '提取图片文字', '学习笔记整理', '日常办公'],
    '专业版': ['工作文档处理', '表格数据提取', '公式识别', '团队协作'],
    '旗舰版': ['批量文档处理', '多格式转换', '企业级应用', '高频使用场景']
};

// 智能推荐问卷数据 - 扩展版
var recommendationQuiz = [
    {
        id: 'usage_scenario',
        question: "您主要在什么场景下使用OCR功能？",
        type: 'single',
        options: [
            {
                text: "个人学习、偶尔使用",
                desc: "扫描笔记、提取图片文字等轻度使用",
                weight: { personal: 4, professional: 1, flagship: 0 },
                tags: ['个人用户', '低频使用']
            },
            {
                text: "日常办公、经常使用",
                desc: "处理工作文档、合同等中等频率使用",
                weight: { personal: 2, professional: 4, flagship: 2 },
                tags: ['办公用户', '中频使用']
            },
            {
                text: "专业工作、高频使用",
                desc: "大量文档处理、数据提取等专业场景",
                weight: { personal: 0, professional: 3, flagship: 4 },
                tags: ['专业用户', '高频使用']
            },
            {
                text: "团队协作、批量处理",
                desc: "多人使用、批量文档处理等企业级应用",
                weight: { personal: 0, professional: 2, flagship: 5 },
                tags: ['企业用户', '批量处理']
            }
        ]
    },
    {
        id: 'daily_usage',
        question: "您预计每天需要识别多少次？",
        type: 'single',
        options: [
            {
                text: "20次以内",
                desc: "轻度使用，偶尔需要",
                weight: { personal: 4, professional: 1, flagship: 0 },
                tags: ['轻度使用']
            },
            {
                text: "20-100次",
                desc: "中等使用频率",
                weight: { personal: 3, professional: 4, flagship: 1 },
                tags: ['中度使用']
            },
            {
                text: "100-500次",
                desc: "较高使用频率",
                weight: { personal: 1, professional: 4, flagship: 3 },
                tags: ['高频使用']
            },
            {
                text: "500次以上",
                desc: "超高频使用或批量处理",
                weight: { personal: 0, professional: 2, flagship: 5 },
                tags: ['超高频使用']
            }
        ]
    },
    {
        id: 'feature_needs',
        question: "您最需要哪些功能？（可多选）",
        type: 'multiple',
        options: [
            {
                text: "基础文字识别",
                desc: "图片转文字的基本功能",
                weight: { personal: 2, professional: 2, flagship: 1 },
                tags: ['基础功能']
            },
            {
                text: "表格识别",
                desc: "识别表格结构和数据",
                weight: { personal: 0, professional: 3, flagship: 3 },
                tags: ['高级功能']
            },
            {
                text: "公式识别",
                desc: "数学公式、化学式等专业内容",
                weight: { personal: 1, professional: 3, flagship: 3 },
                tags: ['专业功能']
            },
            {
                text: "批量处理",
                desc: "一次处理多个文件",
                weight: { personal: 0, professional: 2, flagship: 4 },
                tags: ['效率功能']
            },
            {
                text: "多格式转换",
                desc: "支持多种输出格式",
                weight: { personal: 1, professional: 2, flagship: 4 },
                tags: ['格式功能']
            },
            {
                text: "翻译功能",
                desc: "识别后直接翻译",
                weight: { personal: 2, professional: 2, flagship: 2 },
                tags: ['语言功能']
            }
        ]
    },
    {
        id: 'budget_preference',
        question: "您对价格的考虑是？",
        type: 'single',
        options: [
            {
                text: "价格优先，功能够用就行",
                desc: "希望以最低成本满足基本需求",
                weight: { personal: 4, professional: 1, flagship: 0 },
                tags: ['价格敏感']
            },
            {
                text: "性价比平衡，功能和价格都重要",
                desc: "在合理价格范围内选择功能较全的版本",
                weight: { personal: 2, professional: 4, flagship: 2 },
                tags: ['性价比导向']
            },
            {
                text: "功能优先，价格不是主要考虑",
                desc: "愿意为更好的功能和体验付费",
                weight: { personal: 1, professional: 2, flagship: 4 },
                tags: ['功能导向']
            }
        ]
    },
    {
        id: 'device_usage',
        question: "您通常在几台设备上使用？",
        type: 'single',
        options: [
            {
                text: "1台设备",
                desc: "只在一台电脑上使用",
                weight: { personal: 3, professional: 2, flagship: 1 },
                tags: ['单设备']
            },
            {
                text: "2-3台设备",
                desc: "家里和办公室等多个地方使用",
                weight: { personal: 2, professional: 3, flagship: 2 },
                tags: ['多设备']
            },
            {
                text: "3台以上设备",
                desc: "团队使用或多个工作场所",
                weight: { personal: 0, professional: 2, flagship: 4 },
                tags: ['团队使用']
            }
        ]
    },
    {
        id: 'user_type',
        question: "您的使用场景是？",
        type: 'single',
        options: [
            {
                text: "个人用户",
                desc: "个人日常使用",
                weight: { personal: 4, professional: 2, flagship: 1 },
                tags: ['个人用户']
            },
            {
                text: "企业员工",
                desc: "公司工作需要",
                weight: { personal: 1, professional: 4, flagship: 3 },
                tags: ['企业用户']
            },
            {
                text: "专业人士",
                desc: "律师、会计师、研究员等专业工作",
                weight: { personal: 0, professional: 3, flagship: 4 },
                tags: ['专业用户']
            }
        ]
    }
];

function updateScenarios(versionName) {
    var scenarios = versionScenarios[versionName] || [];
    var scenarioTagsContainer = document.getElementById('scenarioTags');
    if (scenarioTagsContainer) {
        var html = '';
        scenarios.forEach(function(scenario) {
            html += '<span class="scenario-tag">' + scenario + '</span>';
        });
        scenarioTagsContainer.innerHTML = html;
    }
}

// 智能推荐功能 - 增强版
var currentQuizStep = 0;
var quizAnswers = {};
var userProfile = {};

function startRecommendationQuiz() {
    currentQuizStep = 0;
    quizAnswers = {};
    userProfile = {};

    // 清除之前的推荐信息
    window.pendingRecommendation = null;

    var quizHtml = `
    <div class="quiz-modal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;display:flex;align-items:center;justify-content:center;backdrop-filter:blur(3px);">
        <div class="quiz-content" style="background:white;border-radius:20px;padding:0;max-width:600px;width:90%;max-height:90vh;display:flex;flex-direction:column;box-shadow:0 20px 60px rgba(0,0,0,0.3);">
            <!-- 头部 -->
            <div class="quiz-header" style="background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;padding:20px 24px;text-align:center;position:relative;flex-shrink:0;">
                <button onclick="closeQuiz()" style="position:absolute;right:16px;top:16px;background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:18px;transition:all 0.3s ease;">×</button>
                <h3 style="margin:0;font-size:20px;font-weight:600;">🎯 智能版本推荐</h3>
                <p style="margin:8px 0 0;opacity:0.9;font-size:14px;">30秒找到最适合您的方案</p>
                <!-- 进度条 -->
                <div class="progress-bar" style="width:100%;height:4px;background:rgba(255,255,255,0.3);border-radius:2px;margin-top:16px;overflow:hidden;">
                    <div class="progress-fill" id="quizProgress" style="width:0%;height:100%;background:white;border-radius:2px;transition:width 0.3s ease;"></div>
                </div>
                <div class="progress-text" id="progressText" style="font-size:12px;margin-top:8px;opacity:0.8;">第 1 步，共 ${recommendationQuiz.length} 步</div>
            </div>

            <!-- 内容区域 - 可滚动 -->
            <div class="quiz-body" style="padding:24px;flex:1;overflow-y:auto;min-height:0;">
                <div id="quizQuestions"></div>
            </div>

            <!-- 底部操作区 -->
            <div class="quiz-footer" style="padding:16px 24px;border-top:1px solid #f0f0f0;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;">
                <button id="prevBtn" onclick="previousQuestion()" style="background:#f8f9fa;color:#666;border:1px solid #e9ecef;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;transition:all 0.3s ease;font-size:14px;">
                    ← 上一步
                </button>
                <div style="flex:1;"></div>
                <button id="nextBtn" onclick="nextQuestion()" style="background:#007cfa;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;opacity:0.5;pointer-events:none;transition:all 0.3s ease;font-size:14px;">
                    下一步 →
                </button>
                <button id="finishBtn" onclick="showRecommendation()" style="background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;font-weight:600;font-size:14px;">
                    🎉 查看推荐
                </button>
            </div>
        </div>
    </div>`;

    document.body.insertAdjacentHTML('beforeend', quizHtml);
    showQuizQuestion(0);

    // 添加键盘事件监听
    document.addEventListener('keydown', handleQuizKeyboard);
}

function showQuizQuestion(index) {
    if (index >= recommendationQuiz.length) return;

    currentQuizStep = index;
    var question = recommendationQuiz[index];
    var progress = ((index + 1) / recommendationQuiz.length) * 100;

    // 更新进度条
    document.getElementById('quizProgress').style.width = progress + '%';
    document.getElementById('progressText').textContent = `第 ${index + 1} 步，共 ${recommendationQuiz.length} 步`;

    var html = `
    <div class="quiz-question" style="animation:fadeInUp 0.4s ease;">
        <div class="question-header" style="margin-bottom:24px;">
            <h4 style="margin:0 0 8px;color:#333;font-size:18px;font-weight:600;">${question.question}</h4>
            ${question.type === 'multiple' ? '<p style="margin:0;color:#666;font-size:14px;">💡 可以选择多个选项</p>' : ''}
        </div>

        <div class="options-container" style="display:grid;gap:12px;">`;

    question.options.forEach(function(option, i) {
        var inputType = question.type === 'multiple' ? 'checkbox' : 'radio';
        var inputName = question.id;
        var isSelected = quizAnswers[question.id] &&
            (Array.isArray(quizAnswers[question.id]) ?
                quizAnswers[question.id].includes(i) :
                quizAnswers[question.id] === i);

        html += `
        <label class="option-item" style="display:block;padding:16px;border:2px solid #e9ecef;border-radius:12px;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;${isSelected ? 'border-color:#007cfa;background:rgba(0,123,250,0.05);' : ''}"
               onmouseover="this.style.borderColor='#007cfa';this.style.background='rgba(0,123,250,0.05)'"
               onmouseout="if(!this.querySelector('input').checked) {this.style.borderColor='#e9ecef';this.style.background='white'}">
            <div style="display:flex;align-items:flex-start;gap:12px;">
                <input type="${inputType}" name="${inputName}" value="${i}"
                       onchange="handleQuizAnswer('${question.id}', ${i}, '${inputType}')"
                       style="margin-top:2px;transform:scale(1.2);" ${isSelected ? 'checked' : ''}>
                <div style="flex:1;">
                    <div style="font-weight:600;color:#333;margin-bottom:4px;">${option.text}</div>
                    <div style="font-size:13px;color:#666;line-height:1.4;">${option.desc}</div>
                    <div style="margin-top:8px;">
                        ${option.tags.map(tag => `<span style="display:inline-block;background:rgba(0,123,250,0.1);color:#007cfa;padding:2px 8px;border-radius:12px;font-size:11px;margin-right:6px;">${tag}</span>`).join('')}
                    </div>
                </div>
            </div>
        </label>`;
    });

    html += `
        </div>
    </div>`;

    document.getElementById('quizQuestions').innerHTML = html;

    // 更新按钮状态
    updateNavigationButtons();
}

function handleQuizAnswer(questionId, optionIndex, inputType) {
    var question = recommendationQuiz.find(q => q.id === questionId);
    var option = question ? question.options[optionIndex] : null;

    if (inputType === 'checkbox') {
        if (!quizAnswers[questionId]) {
            quizAnswers[questionId] = [];
        }
        var index = quizAnswers[questionId].indexOf(optionIndex);
        if (index > -1) {
            quizAnswers[questionId].splice(index, 1);
        } else {
            quizAnswers[questionId].push(optionIndex);
        }
    } else {
        quizAnswers[questionId] = optionIndex;
    }

    updateNavigationButtons();

    // 自动进入下一题（仅单选题）
    if (inputType === 'radio') {
        setTimeout(function() {
            if (currentQuizStep < recommendationQuiz.length - 1) {
                nextQuestion();
            }
        }, 800);
    }
}

function updateNavigationButtons() {
    var prevBtn = document.getElementById('prevBtn');
    var nextBtn = document.getElementById('nextBtn');
    var finishBtn = document.getElementById('finishBtn');

    // 上一步按钮
    if (currentQuizStep > 0) {
        prevBtn.style.display = 'block';
    } else {
        prevBtn.style.display = 'none';
    }

    // 检查当前问题是否已回答
    var currentQuestion = recommendationQuiz[currentQuizStep];
    var hasAnswer = quizAnswers[currentQuestion.id] !== undefined &&
        (Array.isArray(quizAnswers[currentQuestion.id]) ?
            quizAnswers[currentQuestion.id].length > 0 :
            true);

    if (currentQuizStep === recommendationQuiz.length - 1) {
        // 最后一题
        nextBtn.style.display = 'none';
        finishBtn.style.display = 'block';
        if (hasAnswer) {
            finishBtn.style.opacity = '1';
            finishBtn.style.pointerEvents = 'auto';
        } else {
            finishBtn.style.opacity = '0.5';
            finishBtn.style.pointerEvents = 'none';
        }
    } else {
        // 非最后一题
        nextBtn.style.display = 'block';
        finishBtn.style.display = 'none';
        if (hasAnswer) {
            nextBtn.style.opacity = '1';
            nextBtn.style.pointerEvents = 'auto';
        } else {
            nextBtn.style.opacity = '0.5';
            nextBtn.style.pointerEvents = 'none';
        }
    }
}

function nextQuestion() {
    if (currentQuizStep < recommendationQuiz.length - 1) {
        showQuizQuestion(currentQuizStep + 1);
    }
}

function previousQuestion() {
    if (currentQuizStep > 0) {
        showQuizQuestion(currentQuizStep - 1);
    }
}

function handleQuizKeyboard(event) {
    if (!document.querySelector('.quiz-modal')) return;

    if (event.key === 'Escape') {
        closeQuiz();
    } else if (event.key === 'ArrowRight' || event.key === 'Enter') {
        var nextBtn = document.getElementById('nextBtn');
        var finishBtn = document.getElementById('finishBtn');
        if (nextBtn && nextBtn.style.display !== 'none' && nextBtn.style.pointerEvents !== 'none') {
            nextQuestion();
        } else if (finishBtn && finishBtn.style.display !== 'none' && finishBtn.style.pointerEvents !== 'none') {
            showRecommendation();
        }
    } else if (event.key === 'ArrowLeft') {
        previousQuestion();
    }
}

function showRecommendation() {
    // 计算推荐结果
    var recommendation = calculateRecommendation();

    // 显示推荐结果页面
    showRecommendationResult(recommendation);
}

function calculateRecommendation() {
    var scores = { personal: 0, professional: 0, flagship: 0 };
    var reasons = { personal: [], professional: [], flagship: [] };
    var userTags = [];

    // 遍历所有答案，计算权重分数
    for (var questionId in quizAnswers) {
        var question = recommendationQuiz.find(q => q.id === questionId);
        if (!question) continue;

        var answers = Array.isArray(quizAnswers[questionId]) ? quizAnswers[questionId] : [quizAnswers[questionId]];

        answers.forEach(function(answerIndex) {
            var option = question.options[answerIndex];
            if (!option) return;

            // 累加权重分数
            scores.personal += option.weight.personal || 0;
            scores.professional += option.weight.professional || 0;
            scores.flagship += option.weight.flagship || 0;

            // 收集用户标签
            userTags = userTags.concat(option.tags);

            // 收集推荐理由
            if (option.weight.personal > 2) {
                reasons.personal.push(`${option.text} - 适合个人版的轻量化需求`);
            }
            if (option.weight.professional > 2) {
                reasons.professional.push(`${option.text} - 专业版能更好满足您的需求`);
            }
            if (option.weight.flagship > 2) {
                reasons.flagship.push(`${option.text} - 旗舰版提供最完整的解决方案`);
            }
        });
    }

    // 构建用户画像
    userProfile = {
        tags: [...new Set(userTags)], // 去重
        scores: scores,
        answers: quizAnswers
    };

    // 确定推荐版本
    var maxScore = Math.max(scores.personal, scores.professional, scores.flagship);
    var recommendedVersion = 'personal';

    if (scores.flagship === maxScore) {
        recommendedVersion = 'flagship';
    } else if (scores.professional === maxScore) {
        recommendedVersion = 'professional';
    }

    // 计算匹配度 - 优化算法，增加区分度
    var matchPercentage = 60; // 降低默认值

    try {
        if (maxScore > 0) {
            var totalScore = scores.personal + scores.professional + scores.flagship;
            var scoreRatio = totalScore > 0 ? (maxScore / totalScore) : 0;

            // 计算答题完整度（回答了几个问题）
            var answeredQuestions = Object.keys(quizAnswers).length;
            var completionRatio = answeredQuestions / recommendationQuiz.length;

            // 基础匹配度：根据最高分数和完整度计算
            var baseScore = Math.min(maxScore, 25); // 限制最高分数影响
            var scorePercentage = (baseScore / 25) * 100; // 转换为百分比

            // 基础匹配度：50-85%之间
            matchPercentage = Math.round(50 + (scorePercentage * 0.35));

            // 根据得分优势调整（领先程度）
            var secondScore = 0;
            var sortedScores = [scores.personal, scores.professional, scores.flagship].sort((a, b) => b - a);
            if (sortedScores.length > 1) {
                secondScore = sortedScores[1];
            }
            var advantage = maxScore - secondScore;

            // 优势越大，匹配度越高
            if (advantage >= 8) {
                matchPercentage += 12; // 明显优势
            } else if (advantage >= 5) {
                matchPercentage += 8;  // 较大优势
            } else if (advantage >= 3) {
                matchPercentage += 5;  // 一般优势
            } else if (advantage >= 1) {
                matchPercentage += 2;  // 微弱优势
            }

            // 完整度奖励
            matchPercentage += Math.round(completionRatio * 8);

            // 添加小幅随机因子（减少随机性）
            var confidenceBoost = Math.floor(Math.random() * 4) + 1; // 1-4的随机值
            matchPercentage += confidenceBoost;

            // 确保在合理范围内，增加区分度
            matchPercentage = Math.max(65, Math.min(96, matchPercentage));
        } else {
            // 如果没有得分，给一个中等匹配度
            matchPercentage = 72;
        }
    } catch (e) {
        console.log('匹配度计算出错，使用默认值:', e);
        matchPercentage = 75; // 出错时的兜底值
    }

    // 生成个性化推荐理由
    var personalizedReasons = generatePersonalizedReasons(recommendedVersion, userProfile);

    return {
        version: recommendedVersion,
        scores: scores,
        matchPercentage: matchPercentage,
        reasons: personalizedReasons,
        userProfile: userProfile,
        alternatives: getAlternativeRecommendations(scores, recommendedVersion)
    };
}

function generatePersonalizedReasons(version, profile) {
    var reasons = [];
    var versionNames = {
        'personal': '个人版',
        'professional': '专业版',
        'flagship': '旗舰版'
    };

    // 基于用户标签生成个性化理由

    if (profile.tags.includes('高频使用')) {
        if (version === 'flagship') {
            reasons.push('⚡ 您的高频使用需求，旗舰版的2000次/日额度和极速处理能力最适合');
        } else if (version === 'professional') {
            reasons.push('⚡ 您的使用频率较高，专业版的500次/日额度能很好满足需求');
        }
    }

    if (profile.tags.includes('专业功能')) {
        reasons.push('🔬 您需要的专业功能如公式识别、表格处理，' + versionNames[version] + '都能完美支持');
    }

    if (profile.tags.includes('批量处理')) {
        reasons.push('📊 批量处理是您的核心需求，' + versionNames[version] + '的批量功能将大大提升您的工作效率');
    }

    if (profile.tags.includes('价格敏感')) {
        reasons.push('💰 考虑到您对价格的关注，' + versionNames[version] + '在功能和价格之间达到了最佳平衡');
    }

    if (profile.tags.includes('企业用户')) {
        reasons.push('🏢 作为企业用户，' + versionNames[version] + '的多设备授权和专业支持能满足团队协作需求');
    }

    // 如果理由不足，添加通用理由
    if (reasons.length < 2) {
        switch(version) {
            case 'personal':
                reasons.push('✨ 个人版包含所有基础功能，性价比最高');
                reasons.push('🎯 适合个人用户的日常文字识别需求');
                break;
            case 'professional':
                reasons.push('⚖️ 专业版在功能和价格间达到完美平衡');
                reasons.push('🚀 提供更多高级功能，提升工作效率');
                break;
            case 'flagship':
                reasons.push('👑 旗舰版提供最完整的功能体验');
                reasons.push('🎪 适合对功能要求较高的专业用户');
                break;
        }
    }

    return reasons.slice(0, 4); // 最多返回4个理由
}

function getAlternativeRecommendations(scores, recommended) {
    var alternatives = [];
    var sortedVersions = Object.keys(scores).sort((a, b) => scores[b] - scores[a]);

    sortedVersions.forEach(function(version) {
        if (version !== recommended && scores[version] > 0) {
            var reason = '';
            switch(version) {
                case 'personal':
                    reason = '如果预算有限，个人版也能满足基本需求';
                    break;
                case 'professional':
                    reason = '如果需要更多专业功能，专业版是不错的选择';
                    break;
                case 'flagship':
                    reason = '如果追求最佳体验，旗舰版功能最全面';
                    break;
            }
            alternatives.push({
                version: version,
                score: scores[version],
                reason: reason
            });
        }
    });

    return alternatives.slice(0, 2); // 最多返回2个备选方案
}

function getReasonCategory(reason, tags) {
    if (reason.includes('价格') || reason.includes('性价比') || reason.includes('经济')) {
        return '💰 性价比分析';
    } else if (reason.includes('功能') || reason.includes('需求') || reason.includes('适合')) {
        return '🎯 需求匹配';
    } else if (reason.includes('使用') || reason.includes('频率') || reason.includes('次数')) {
        return '📊 使用习惯';
    } else if (reason.includes('工作') || reason.includes('企业') || reason.includes('专业')) {
        return '💼 场景分析';
    } else {
        return '✨ 智能推荐';
    }
}

function generateUserSummary(tags, recommendedVersion) {
    var summaries = {
        'personal': {
            '企业用户': '您是注重效率的职场人士，个人版能满足您的日常工作需求',
            '专业用户': '作为专业人士，个人版为您提供了经济实用的解决方案',
            '个人用户': '您是理性的个人用户，个人版的功能配置最符合您的使用习惯',
            'default': '您是追求性价比的理性用户，个人版是您的最佳选择'
        },
        'professional': {
            '企业用户': '您是高效的企业用户，专业版的强大功能助力您的工作',
            '专业用户': '作为专业人士，专业版的高级功能正是您所需要的',
            '个人用户': '您对功能有更高要求，专业版能满足您的进阶需求',
            'default': '您是追求功能与性价比平衡的用户，专业版最适合您'
        },
        'flagship': {
            '企业用户': '您是追求极致效率的企业精英，旗舰版为您提供顶级体验',
            '专业用户': '作为资深专业人士，旗舰版的全功能配置是您的不二选择',
            '个人用户': '您对品质有极高要求，旗舰版能满足您的所有需求',
            'default': '您是追求极致体验的用户，旗舰版为您提供最强大的功能'
        }
    };

    var versionSummaries = summaries[recommendedVersion];
    for (var tag of tags) {
        if (versionSummaries[tag]) {
            return versionSummaries[tag];
        }
    }
    return versionSummaries.default;
}

function getTagContribution(tag, recommendedVersion) {
    var contributions = {
        '个人用户': { weight: 15, percentage: 75, description: '日常使用场景匹配' },
        '企业用户': { weight: 20, percentage: 85, description: '工作效率需求匹配' },
        '专业用户': { weight: 25, percentage: 95, description: '专业功能需求匹配' },
        '高频使用': { weight: 18, percentage: 80, description: '使用频率分析' },
        '团队使用': { weight: 22, percentage: 90, description: '协作需求匹配' },
        '预算敏感': { weight: 12, percentage: 60, description: '性价比考量' },
        '功能需求': { weight: 20, percentage: 85, description: '功能匹配度' }
    };

    return contributions[tag] || { weight: 10, percentage: 50, description: '综合因素考量' };
}

function showRecommendationResult(recommendation) {
    // 保存推荐对象到全局变量，供动画函数使用
    window.currentRecommendation = recommendation;

    var versionNames = {
        'personal': '个人版',
        'professional': '专业版',
        'flagship': '旗舰版'
    };

    var versionColors = {
        'personal': '#6666FF',
        'professional': '#4B4B4B',
        'flagship': '#E6D700'
    };

    var recommendedVersionName = versionNames[recommendation.version];
    var primaryColor = versionColors[recommendation.version];

    // 生成节省时间提示
    var timeSaved = Math.floor(Math.random() * 10) + 15; // 15-25分钟

    var resultHtml = `
    <div class="recommendation-result" style="padding:24px;text-align:center;">
        <!-- 庆祝动画和成就感 -->
        <div class="celebration-header" style="margin-bottom:32px;animation:celebrationPulse 1s ease-out;text-align:center;">
            <div style="font-size:64px;margin-bottom:16px;animation:bounce 1.5s ease-out;">🎉</div>
            <div style="background:linear-gradient(135deg,#28a745,#20c997);color:white;padding:10px 20px;border-radius:20px;display:inline-block;font-size:14px;font-weight:600;box-shadow:0 2px 8px rgba(40,167,69,0.3);">
                ✨ 分析完成！为您节省了 ${timeSaved} 分钟选择时间
            </div>

        </div>

        <!-- 推荐版本卡片 -->
        <div class="recommended-version-card" style="background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border:2px solid ${primaryColor}30;border-radius:20px;padding:24px;margin-bottom:32px;position:relative;animation:scaleIn 0.8s ease-out 0.3s both;">
            <!-- AI推荐徽章 -->
            <div style="position:absolute;top:-12px;right:20px;background:linear-gradient(135deg,#FF6B6B,#FF8E8E);color:white;padding:6px 12px;border-radius:12px;font-size:11px;font-weight:700;box-shadow:0 2px 8px rgba(255,107,107,0.4);">
                🤖 AI智能推荐
            </div>

            <!-- 版本名称和匹配度布局 -->
            <div class="version-header" style="text-align:center;margin-bottom:20px;">
                <!-- 版本名称 -->
                <div style="font-size:32px;font-weight:900;color:${primaryColor};text-shadow:0 2px 4px rgba(0,0,0,0.1);margin-bottom:16px;">
                    ${recommendedVersionName}
                </div>

                <!-- 匹配度圆形进度条 -->
                <div class="match-percentage" style="display:inline-block;">
                    <div style="position:relative;width:100px;height:100px;">
                        <svg width="100" height="100" style="transform:rotate(-90deg);">
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="6"/>
                            <circle cx="50" cy="50" r="40" fill="none" stroke="${primaryColor}" stroke-width="6"
                                    stroke-dasharray="251" stroke-dashoffset="${251 - (251 * recommendation.matchPercentage / 100)}"
                                    style="animation:progressFill 2s ease-out 0.5s both;"/>
                        </svg>
                        <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;">
                            <div style="font-size:20px;font-weight:900;color:${primaryColor};" data-target="${recommendation.matchPercentage}">${recommendation.matchPercentage}</div>
                            <div id="match-label" style="font-size:10px;color:#666;font-weight:600;margin-top:2px;">匹配度</div>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- 推荐理由 -->
        <div class="recommendation-reasons" style="margin-bottom:32px;text-align:left;animation:slideInUp 0.8s ease-out 0.6s both;">
            <h4 style="text-align:center;margin:0 0 24px;color:#333;font-size:18px;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-size:20px;">🧠</span>
                AI分析结果
            </h4>
            <div style="display:flex;flex-direction:column;gap:16px;width:100%;max-width:500px;margin:0 auto;">
                ${recommendation.reasons.map((reason, index) => `
                    <div style="display:flex;align-items:flex-start;gap:16px;padding:20px;background:linear-gradient(135deg,rgba(255,255,255,0.9),rgba(255,255,255,0.6));border-radius:16px;border:1px solid ${primaryColor}20;box-shadow:0 2px 12px rgba(0,0,0,0.08);animation:slideInLeft 0.6s ease-out ${0.8 + index * 0.1}s both;min-height:80px;width:100%;box-sizing:border-box;">
                        <div style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;flex-shrink:0;box-shadow:0 2px 8px ${primaryColor}40;">${index + 1}</div>
                        <div style="flex:1;display:flex;flex-direction:column;justify-content:center;min-width:0;">
                            <div style="color:#333;line-height:1.6;font-size:15px;margin-bottom:8px;word-wrap:break-word;">${reason}</div>
                            <div style="font-size:12px;color:${primaryColor};font-weight:600;">
                                ${getReasonCategory(reason, recommendation.userProfile.tags)}
                            </div>
                        </div>
                        <div style="color:${primaryColor};font-size:18px;opacity:0.7;align-self:center;flex-shrink:0;">✓</div>
                    </div>
                `).join('')}
            </div>
        </div>

        <!-- 适配度分析 -->
        <div class="version-comparison" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 0.9s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="font-size:20px;">📊</span>
                适配度分析
            </h4>
            <div style="display:grid;gap:16px;max-width:450px;margin:0 auto;">
                ${Object.keys(recommendation.scores).map((version, index) => {
                    var score = recommendation.scores[version];
                    var maxScore = Math.max(...Object.values(recommendation.scores));
                    var percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
                    var isRecommended = version === recommendation.version;

                    return `
                    <div style="display:flex;align-items:center;gap:16px;padding:16px;border-radius:12px;${isRecommended ? `background:linear-gradient(135deg,${primaryColor}10,${primaryColor}05);border:2px solid ${primaryColor}30;` : 'background:#f8f9fa;border:2px solid #e9ecef;'}animation:slideInLeft 0.6s ease-out ${1.1 + index * 0.1}s both;">
                        <div style="font-weight:600;color:#333;min-width:70px;font-size:15px;">${versionNames[version]}</div>
                        <div style="flex:1;background:#e9ecef;height:10px;border-radius:5px;overflow:hidden;">
                            <div style="width:${percentage}%;height:100%;background:${isRecommended ? primaryColor : '#adb5bd'};border-radius:5px;transition:width 1s ease 0.5s;"></div>
                        </div>
                        <div style="font-weight:700;color:${isRecommended ? primaryColor : '#666'};min-width:45px;font-size:15px;">${score}分</div>
                        ${isRecommended ? `<div style="color:${primaryColor};font-size:18px;">⭐</div>` : ''}
                    </div>`;
                }).join('')}
            </div>
        </div>

        <!-- 用户画像分析 -->
        <div class="user-profile" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.1s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="font-size:20px;">👤</span>
                您的专属画像
            </h4>

            <!-- 画像总结 -->
            <div style="background:linear-gradient(135deg,rgba(255,255,255,0.95),rgba(255,255,255,0.8));padding:20px;border-radius:16px;border:1px solid ${primaryColor}20;margin-bottom:20px;text-align:center;box-shadow:0 2px 12px rgba(0,0,0,0.08);">
                <div style="font-size:16px;color:#333;font-weight:600;margin-bottom:8px;">
                    ${generateUserSummary(recommendation.userProfile.tags, recommendation.version)}
                </div>
                <div style="font-size:13px;color:#666;">
                    基于您的选择，AI为您生成的个性化标签
                </div>
            </div>

            <!-- 标签及其贡献度 -->
            <div style="display:grid;gap:12px;max-width:500px;margin:0 auto;">
                ${recommendation.userProfile.tags.map((tag, index) => {
                    var contribution = getTagContribution(tag, recommendation.version);
                    return `
                    <div style="display:flex;align-items:center;justify-content:space-between;padding:16px;background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border-radius:12px;border:1px solid ${primaryColor}15;animation:slideInRight 0.6s ease-out ${1.3 + index * 0.1}s both;">
                        <div style="display:flex;align-items:center;gap:12px;">
                            <span style="background:${primaryColor};color:white;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;">${tag}</span>
                            <span style="font-size:13px;color:#666;">${contribution.description}</span>
                        </div>
                        <div style="display:flex;align-items:center;gap:8px;">
                            <div style="font-size:12px;color:${primaryColor};font-weight:600;">+${contribution.weight}分</div>
                            <div style="width:40px;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden;">
                                <div style="width:${contribution.percentage}%;height:100%;background:${primaryColor};border-radius:3px;"></div>
                            </div>
                        </div>
                    </div>
                    `;
                }).join('')}
            </div>
        </div>

        <!-- 备选方案 -->
        ${recommendation.alternatives.length > 0 ? `
        <div class="alternatives" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.5s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:16px;text-align:center;">🤔 其他选择</h4>
            <div style="display:flex;flex-direction:column;gap:12px;max-width:500px;margin:0 auto;">
                ${recommendation.alternatives.map((alt, index) => `
                    <div onclick="selectAlternativeVersion('${alt.version}')"
                         style="display:flex;align-items:center;justify-content:space-between;padding:16px 20px;background:#f8f9fa;border-radius:12px;border:1px solid #e9ecef;cursor:pointer;transition:all 0.3s ease;animation:slideInRight 0.6s ease-out ${1.7 + index * 0.15}s both;"
                         onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#2196f3';this.style.transform='translateY(-1px)';this.style.boxShadow='0 2px 8px rgba(33,150,243,0.2)'"
                         onmouseout="this.style.background='#f8f9fa';this.style.borderColor='#e9ecef';this.style.transform='translateY(0)';this.style.boxShadow='none'">
                        <div style="flex:1;">
                            <div style="font-weight:600;color:#333;font-size:15px;margin-bottom:4px;">${versionNames[alt.version]}</div>
                            <div style="font-size:13px;color:#666;line-height:1.4;">${alt.reason}</div>
                        </div>
                        <div style="color:#2196f3;font-size:18px;margin-left:12px;">→</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        <!-- 行动引导区域 -->
        <div class="action-section" style="text-align:center;animation:slideInUp 0.8s ease-out 2s both;">
            <!-- 推荐提示 -->
            <div style="background:linear-gradient(135deg,${primaryColor}12,${primaryColor}06);border-radius:16px;padding:24px;margin-bottom:24px;border:1px solid ${primaryColor}25;position:relative;overflow:hidden;">
                <!-- 背景装饰 -->
                <div style="position:absolute;top:-20px;right:-20px;width:80px;height:80px;background:${primaryColor}15;border-radius:50%;"></div>
                <div style="position:absolute;bottom:-30px;left:-30px;width:100px;height:100px;background:${primaryColor}08;border-radius:50%;"></div>

                <div style="position:relative;z-index:1;">
                    <div style="font-size:18px;color:#333;margin-bottom:12px;font-weight:600;">
                        <span style="color:${primaryColor};font-size:20px;">🎯</span>
                        推荐您选择 <strong style="color:${primaryColor};">${recommendedVersionName}</strong>
                    </div>

                    <!-- 保障信息 -->
                    <div style="display:flex;justify-content:center;gap:20px;flex-wrap:wrap;">
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            7天无理由退款
                        </div>
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            稳定服务保障
                        </div>
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            专业客服支持
                        </div>
                    </div>
                </div>
            </div>

            <!-- 按钮组 -->
            <div style="display:flex;justify-content:center;gap:16px;flex-wrap:wrap;">
                <button onclick="applyRecommendation('${recommendation.version}')"
                        style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;border:none;padding:16px 32px;border-radius:25px;font-size:16px;font-weight:700;cursor:pointer;box-shadow:0 4px 16px ${primaryColor}40;transition:all 0.3s ease;position:relative;overflow:hidden;"
                        onmouseover="this.style.transform='translateY(-2px)';this.style.boxShadow='0 6px 20px ${primaryColor}50'"
                        onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 4px 16px ${primaryColor}40'">
                    <span style="position:relative;z-index:1;">🚀 立即选择${recommendedVersionName}</span>
                </button>

                <button onclick="restartQuiz()"
                        style="background:linear-gradient(135deg,#6c757d,#5a6268);color:white;border:none;padding:14px 24px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;box-shadow:0 2px 8px rgba(108,117,125,0.3);transition:all 0.3s ease;"
                        onmouseover="this.style.transform='translateY(-1px)';this.style.boxShadow='0 4px 12px rgba(108,117,125,0.4)'"
                        onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 2px 8px rgba(108,117,125,0.3)'">
                    🔄 重新推荐
                </button>

                <button onclick="closeQuiz()"
                        style="background:transparent;color:#6c757d;border:2px solid #dee2e6;padding:12px 20px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.3s ease;"
                        onmouseover="this.style.color='#495057';this.style.borderColor='#adb5bd';this.style.background='#f8f9fa'"
                        onmouseout="this.style.color='#6c757d';this.style.borderColor='#dee2e6';this.style.background='transparent'">
                    关闭
                </button>
            </div>
        </div>
    </div>`;

    // 更新问卷内容
    document.getElementById('quizQuestions').innerHTML = resultHtml;

    // 隐藏导航按钮
    document.getElementById('prevBtn').style.display = 'none';
    document.getElementById('nextBtn').style.display = 'none';
    document.getElementById('finishBtn').style.display = 'none';

    // 更新头部信息
    document.querySelector('.quiz-header h3').innerHTML = '🎉 推荐结果';
    document.querySelector('.quiz-header p').innerHTML = '基于您的需求分析得出的个性化推荐';
    document.getElementById('quizProgress').style.width = '100%';
    document.getElementById('progressText').textContent = '分析完成';

    // 保存推荐结果到本地存储
    try {
        localStorage.setItem('ocrRecommendation', JSON.stringify({
            result: recommendation,
            timestamp: Date.now()
        }));
    } catch(e) {
        console.log('无法保存推荐结果到本地存储');
    }

    // 保存推荐版本信息，用于关闭时自动切换
    window.pendingRecommendation = {
        version: recommendation.version,
        userHasSelected: false, // 标记用户是否手动选择了版本
        selectedVersion: null, // 用户手动选择的版本
        isApplyRecommendation: false // 是否是点击"应用推荐"按钮
    };

    // 启动数字动画 - 确保DOM渲染完成后再执行
    setTimeout(function() {
        animateCountUp();
        // 清理动画样式
        cleanupAnimations();

        // 2-3秒后隐藏庆祝动画并缩小匹配度显示
        var hideDelay = Math.floor(Math.random() * 1000) + 2000; // 2-3秒随机延迟
        setTimeout(function() {
            hideCelebrationAndCompactView();
        }, hideDelay);
    }, 1200); // 增加延迟时间，确保DOM完全渲染
}

function animateCountUp() {
    var countElement = document.querySelector('[data-target]');
    if (!countElement) {
        return;
    }

    // 直接从全局推荐对象获取匹配度
    var target = 85; // 默认值
    if (window.currentRecommendation && window.currentRecommendation.matchPercentage) {
        target = parseInt(window.currentRecommendation.matchPercentage);
    }

    // 重置显示为0，开始动画
    countElement.textContent = '0';

    var current = 0;
    var increment = target / 50; // 约1.5秒内完成，50帧
    var timer = setInterval(function() {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        countElement.textContent = Math.floor(current);
    }, 30); // 约33fps，更流畅
}

function hideCelebrationAndCompactView() {
    // 先获取数据，再隐藏元素
    var versionHeader = document.querySelector('.version-header');
    var recommendedVersion = '个人版'; // 默认值
    var matchPercentage = '95%'; // 默认值

    // 从版本头部获取数据
    if (versionHeader) {
        var versionTitle = versionHeader.querySelector('div[style*="font-size:32px"]');
        if (versionTitle) {
            recommendedVersion = versionTitle.textContent || '个人版';
        }

        var matchCircle = versionHeader.querySelector('div[data-target]');
        if (matchCircle) {
            matchPercentage = matchCircle.textContent || matchCircle.getAttribute('data-target') || '95%';
        }
    }

    // 隐藏动画容器
    var animationContainer = document.querySelector('.recommendation-animation');
    if (animationContainer) {
        animationContainer.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        animationContainer.style.opacity = '0';
        animationContainer.style.transform = 'translateY(-20px)';
        setTimeout(function() {
            animationContainer.style.display = 'none';
        }, 400);
    }

    // 隐藏庆祝头部 - 使用更平滑的动画
    var celebrationHeader = document.querySelector('.celebration-header');
    if (celebrationHeader) {
        celebrationHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        celebrationHeader.style.opacity = '0';
        celebrationHeader.style.transform = 'translateY(-8px) scale(0.98)';

        // 完全隐藏元素
        setTimeout(function() {
            celebrationHeader.style.display = 'none';
        }, 400);
    }

    // 完全隐藏推荐版本卡片
    var versionCard = document.querySelector('.recommended-version-card');
    if (versionCard) {
        versionCard.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        versionCard.style.opacity = '0';
        versionCard.style.transform = 'translateY(-20px) scale(0.95)';
        setTimeout(function() {
            versionCard.style.display = 'none';
        }, 400);
    }

    // 隐藏原有的版本头部区域
    if (versionHeader) {
        versionHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        versionHeader.style.opacity = '0';
        versionHeader.style.transform = 'translateY(-10px)';
        setTimeout(function() {
            versionHeader.style.display = 'none';
        }, 400);
    }

    // 压缩推荐理由部分
    var reasonsSection = document.querySelector('.recommendation-reasons');
    if (reasonsSection) {
        reasonsSection.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        reasonsSection.style.marginBottom = '16px'; // 减少底部间距

        // 缩小标题
        var title = reasonsSection.querySelector('h4');
        if (title) {
            title.style.fontSize = '14px';
            title.style.marginBottom = '12px';
        }

        // 压缩推荐理由项目，变成一行显示
        var reasonItems = reasonsSection.querySelectorAll('div[style*="display:flex"][style*="gap:16px"]');
        reasonItems.forEach(function(item, index) {
            item.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            item.style.padding = '8px 12px'; // 减少内边距
            item.style.fontSize = '12px'; // 缩小字体
            item.style.borderRadius = '8px'; // 减少圆角

            // 缩小序号圆圈
            var numberCircle = item.querySelector('div[style*="width:32px"]');
            if (numberCircle) {
                numberCircle.style.width = '20px';
                numberCircle.style.height = '20px';
                numberCircle.style.fontSize = '10px';
            }
        });

        // 将推荐理由改为水平排列（如果空间允许）
        var reasonsGrid = reasonsSection.querySelector('div[style*="display:grid"]');
        if (reasonsGrid && reasonItems.length <= 3) {
            reasonsGrid.style.display = 'flex';
            reasonsGrid.style.flexWrap = 'wrap';
            reasonsGrid.style.gap = '8px';
            reasonsGrid.style.justifyContent = 'center';
        }
    }

    // 创建新的简洁结果展示区块
    var resultContainer = document.querySelector('.recommendation-result');
    if (resultContainer) {
        // 检查是否已经创建了简洁结果区块
        var compactResult = resultContainer.querySelector('.compact-result');
        if (!compactResult) {
            // 生成动态的用户选择百分比（88-96%之间）
            var userChoicePercentage = Math.floor(Math.random() * 9) + 88; // 88-96%

            // 根据推荐版本调整用户选择率
            var versionMultiplier = 1;
            if (recommendedVersion.includes('个人')) {
                versionMultiplier = 0.95; // 个人版稍低一些
            } else if (recommendedVersion.includes('专业')) {
                versionMultiplier = 1.02; // 专业版稍高一些
            } else if (recommendedVersion.includes('旗舰')) {
                versionMultiplier = 1.05; // 旗舰版最高
            }

            userChoicePercentage = Math.min(96, Math.round(userChoicePercentage * versionMultiplier));

            // 生成动态的性价比星级（4-5星）
            var costEffectivenessStars = Math.random() > 0.3 ? '⭐⭐⭐⭐⭐' : '⭐⭐⭐⭐☆';

            // 生成动态的功能匹配描述
            var functionalMatchTexts = ['完美适配', '高度匹配', '精准匹配', '理想选择'];
            var functionalMatchText = functionalMatchTexts[Math.floor(Math.random() * functionalMatchTexts.length)];

            // 创建简洁的结果展示区块
            var compactResultHtml = `
                <div class="compact-result" style="
                    background: linear-gradient(135deg, rgba(0,123,250,0.08), rgba(0,123,250,0.12));
                    border-radius: 16px;
                    padding: 20px;
                    margin: 16px 0;
                    text-align: center;
                    border: 2px solid rgba(0,123,250,0.2);
                    animation: slideInUp 0.5s ease-out;
                    box-shadow: 0 4px 20px rgba(0,123,250,0.1);
                ">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 16px; margin-bottom: 16px;">
                        <div style="
                            background: linear-gradient(135deg, #007bfa, #0056b3);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-size: 16px;
                            font-weight: 700;
                            box-shadow: 0 4px 12px rgba(0,123,250,0.3);
                        ">
                            推荐方案：${recommendedVersion}
                        </div>
                        <div style="
                            background: rgba(0,123,250,0.15);
                            color: #007bfa;
                            padding: 6px 12px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                        ">
                            匹配度：${matchPercentage}
                        </div>
                    </div>

                    <div style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                        gap: 12px;
                        margin-top: 16px;
                    ">
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">性价比</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${costEffectivenessStars}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">功能匹配</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${functionalMatchText}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">用户选择</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${userChoicePercentage}%同选</div>
                        </div>
                    </div>
                </div>
            `;

            // 插入到结果容器的开头
            resultContainer.insertAdjacentHTML('afterbegin', compactResultHtml);
        }

        // 调整容器样式
        resultContainer.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        resultContainer.style.paddingTop = '8px';
        resultContainer.style.paddingBottom = '16px';
    }
}

function cleanupAnimations() {
    // 3秒后清理动画样式，减少内存占用
    setTimeout(function() {
        var animatedElements = document.querySelectorAll('[style*="animation"]');
        animatedElements.forEach(function(element) {
            var style = element.getAttribute('style');
            if (style) {
                // 移除animation相关样式，保留其他样式
                var newStyle = style.replace(/animation:[^;]*;?/g, '');
                element.setAttribute('style', newStyle);
            }
        });
    }, 3000);
}

function applyRecommendation(version) {
    // 标记为应用推荐（需要弹框提示）
    if (window.pendingRecommendation) {
        window.pendingRecommendation.isApplyRecommendation = true;
        window.pendingRecommendation.userHasSelected = true;
    }

    closeQuiz();
}

function autoSwitchVersion(version) {
    // 找到对应的版本标签并点击（但不关闭推荐界面）
    var versionMap = {
        'personal': '个人版',
        'professional': '专业版',
        'flagship': '旗舰版'
    };

    var versionName = versionMap[version];
    var versionTab = Array.from(document.querySelectorAll('.version-tab')).find(function(tab) {
        return tab.querySelector('.tab-name').textContent === versionName;
    });

    if (versionTab && !versionTab.classList.contains('active')) {
        // 点击版本标签切换
        versionTab.click();

        // 显示切换成功的提示
        showRecommendationToast(versionName);
    }
}

function selectAlternativeVersion(version) {
    // 标记用户手动选择了其他版本（不需要弹框提示）
    if (window.pendingRecommendation) {
        window.pendingRecommendation.userHasSelected = true;
        window.pendingRecommendation.selectedVersion = version;
        window.pendingRecommendation.isApplyRecommendation = false;
    }

    // 关闭推荐界面
    closeQuiz();
}

function restartQuiz() {
    currentQuizStep = 0;
    quizAnswers = {};
    userProfile = {};

    // 清除待处理的推荐信息
    window.pendingRecommendation = null;

    showQuizQuestion(0);
}

function closeQuiz() {
    var modal = document.querySelector('.quiz-modal');
    if (modal) {
        modal.remove();
        document.removeEventListener('keydown', handleQuizKeyboard);

        // 延迟一点时间让页面重新布局，然后处理版本切换
        setTimeout(function() {
            if (window.pendingRecommendation) {
                if (!window.pendingRecommendation.userHasSelected) {
                    // 情况1：用户直接关闭窗口，自动应用推荐（需要弹框提示）
                    switchToVersionWithToast(window.pendingRecommendation.version, true);


                } else if (window.pendingRecommendation.isApplyRecommendation) {
                    // 情况2：用户点击"应用推荐"（需要弹框提示）
                    switchToVersionWithToast(window.pendingRecommendation.version, true);
                } else if (window.pendingRecommendation.selectedVersion) {
                    // 情况3：用户选择了其他版本（不需要弹框提示）
                    switchToVersionWithToast(window.pendingRecommendation.selectedVersion, false);
                }
            }

            // 清除待处理的推荐信息
            window.pendingRecommendation = null;
        }, 200);
    }
}

function switchToVersionWithToast(versionType, showToast) {
    // 找到对应的版本标签
    var versionMap = {
        'personal': '个人版',
        'professional': '专业版',
        'flagship': '旗舰版'
    };

    var versionName = versionMap[versionType];
    var versionTab = Array.from(document.querySelectorAll('.version-tab')).find(function(tab) {
        return tab.querySelector('.tab-name').textContent === versionName;
    });

    if (versionTab) {
        // 切换到指定版本
        if (!versionTab.classList.contains('active')) {
            versionTab.click();
        }

        // 根据参数决定是否显示弹框提示
        if (showToast) {
            showRecommendationToast(versionName);
        }

        // 滚动到版本选择区域
        versionTab.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

function highlightRecommendedVersion(versionType) {
    // 移除所有推荐标识和动画
    document.querySelectorAll('.recommendation-highlight').forEach(function(el) {
        el.remove();
    });
    document.querySelectorAll('.version-tab').forEach(function(tab) {
        tab.style.animation = '';
        tab.style.transform = '';
        tab.classList.remove('ai-recommended');
    });

    // 调用新的切换函数
    switchToVersionWithToast(versionType, true);
}

function showRecommendationToast(versionName) {
    // 移除可能存在的旧提示
    var existingToast = document.querySelector('.recommendation-toast');
    if (existingToast) {
        existingToast.remove();
    }

    var toast = document.createElement('div');
    toast.className = 'recommendation-toast';
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        z-index: 10000;
        box-shadow: 0 8px 24px rgba(40, 167, 69, 0.3);
        max-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fa fa-check-circle" style="font-size: 16px;"></i>
            <div>
                <div>已为您选择 ${versionName}</div>
                <div style="font-size: 12px; opacity: 0.9; margin-top: 2px;">基于AI智能分析推荐</div>
            </div>
        </div>
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(function() {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 10);

    // 4秒后隐藏并移除
    setTimeout(function() {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(function() {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 4000);
}

var isExpanded=false;
function initCurrentVersionFeatures(){updateCurrentVersionFeatures()}
function updateCurrentVersionFeatures(){
    var selectedTab=document.querySelector('.version-tab.active');
    var typeName=selectedTab.querySelector('.tab-name').textContent;
    var versionClass=getVersionClass(selectedTab);
    var typeDesc=selectedTab.getAttribute('data-desc');
    document.getElementById('featuresTitle').textContent=typeDesc;
    var featuresSection=document.getElementById('versionFeaturesSection');
    featuresSection.className=featuresSection.className.replace(/\b(personal|professional|flagship)\b/g,'')+' '+versionClass;
    var features=versionCoreFeatures[typeName]||[];
    var featuresHtml='';
    var displayCount=isExpanded?features.length:Math.min(4,features.length);
    for(var i=0;i<displayCount;i++){
        var feature=features[i];
        featuresHtml+='<div class="feature-item"><i class="fa fa-check"></i><div class="feature-content"><div class="feature-name">'+feature.name+'</div>';
        if(feature.desc)
        featuresHtml+='<div class="feature-desc">'+feature.desc+'</div>';
        featuresHtml+='</div></div>'
    }
    document.getElementById('featuresGrid').innerHTML=featuresHtml;
    var expandSection=document.getElementById('featuresExpand');
    if(features.length>4){
        expandSection.style.display='block';
        document.getElementById('expandText').textContent=isExpanded?'收起':'展开';
        var icon=document.getElementById('expandIcon');
        icon.className=isExpanded?'fa fa-angle-up':'fa fa-angle-down';
        document.querySelector('.expand-btn').classList.toggle('expanded',isExpanded)}
        else{expandSection.style.display='none'}

    // 更新使用场景
    updateScenarios(typeName);
    }
function toggleFeatures(event){if(event){event.preventDefault();event.stopPropagation()}isExpanded=!isExpanded;updateCurrentVersionFeatures();return false}
function getVersionClass(tab){if(tab.classList.contains('personal'))return 'personal';if(tab.classList.contains('professional'))return 'professional';if(tab.classList.contains('flagship'))return 'flagship';return 'professional'}
document.addEventListener('DOMContentLoaded',function(){
    initCurrentVersionFeatures();
    var selectedTab=document.querySelector('.version-tab.active');
    updateVersionTheme(selectedTab);
    updateOrderSummary();
    initFormValidation();
    animateUserCount();
    startTestimonialSlider();
});

function initFormValidation(){
    var accountInput=document.getElementById('txtAccount');
    if(accountInput){
        accountInput.addEventListener('input',function(){
            var account=this.value.trim();
            var isValid=phoneReg.test(account)||emailReg.test(account);
            this.style.borderColor=account===''?'#e9ecef':isValid?'#28a745':'#dc3545';
            if(account!==''&&!isValid){
                this.style.boxShadow='0 0 0 3px rgba(220,53,69,0.1)';
            }else if(isValid){
                this.style.boxShadow='0 0 0 3px rgba(40,167,69,0.1)';
            }else{
                this.style.boxShadow='none';
            }
        });
        accountInput.addEventListener('blur',function(){
            if(this.style.borderColor==='rgb(220, 53, 69)'){
                this.style.boxShadow='0 0 0 3px rgba(220,53,69,0.1)';
            }
        });
    }
}

function animateUserCount(){
    var userCountElement=document.querySelector('.user-count');
    if(userCountElement){
        var targetNumber=50000;
        var currentNumber=0;
        var increment=targetNumber/100;
        var timer=setInterval(function(){
            currentNumber+=increment;
            if(currentNumber>=targetNumber){
                currentNumber=targetNumber;
                clearInterval(timer);
            }
            userCountElement.textContent=Math.floor(currentNumber).toLocaleString()+'+';
        },20);
    }
}

function startTestimonialSlider(){
    var slider=document.getElementById('testimonialSlider');
    var items=slider.querySelectorAll('.testimonial-item');
    if(items.length===0)return;
    var currentIndex=0;
    var containerHeight=60;
    var itemHeight=containerHeight+8;
    setInterval(function(){
        currentIndex=(currentIndex+1)%items.length;
        slider.style.transform='translateY(-'+currentIndex*itemHeight+'px)';
    },3000);
}

function selectVersion(typeHash){
// 获取选中的版本标签
var selectedTab = document.querySelector('.version-tab[data-type="'+typeHash+'"]');

// 切换版本标签
document.querySelectorAll('.version-tab').forEach(tab=>tab.classList.remove('active'));
selectedTab.classList.add('active');

// 切换价格选项区域
document.querySelectorAll('.pricing-options').forEach(option=>option.classList.remove('active'));
var targetPricingOptions=document.querySelector('.pricing-options[data-type="'+typeHash+'"]');
targetPricingOptions.classList.add('active');

// 清除所有价格项的选中状态
document.querySelectorAll('.pricing-item').forEach(item=>item.classList.remove('selected'));

// 重新选择当前版本的默认项
var defaultPricing=null;

// 首先查找标记为IsDefault=true的项
defaultPricing=targetPricingOptions.querySelector('.pricing-item[data-is-default="true"]');

// 如果没有找到IsDefault的项，选择第一个
if(!defaultPricing){
defaultPricing=targetPricingOptions.querySelector('.pricing-item');
}

// 设置选中状态
if(defaultPricing){
defaultPricing.classList.add('selected');
}

updateVersionTheme(selectedTab);
updateOrderSummary();
updateCurrentVersionFeatures();
}
function updateVersionTheme(selectedTab){
    var versionClass=getVersionClass(selectedTab);
    document.querySelectorAll('.pricing-item').forEach(item=>{
        item.classList.remove('personal','professional','flagship');
        item.classList.add(versionClass);
    });
    document.querySelectorAll('.btn-upgrade').forEach(btn=>{
        btn.className=btn.className.replace(/\b(personal|professional|flagship)\b/g,'')+' '+versionClass;
    });
    var featuresSection=document.getElementById('versionFeaturesSection');
    featuresSection.className=featuresSection.className.replace(/\b(personal|professional|flagship)\b/g,'')+' '+versionClass;
}
function selectPricing(element){
// 获取价格选择相关信息
var planName = element.getAttribute('data-name');
var price = element.getAttribute('data-price');
var selectedVersion = document.querySelector('.version-tab.active .tab-name').textContent;

    document.querySelectorAll('.pricing-item').forEach(item=>item.classList.remove('selected'));
    element.classList.add('selected');
    var selectedTab=document.querySelector('.version-tab.active');
    var versionClass=getVersionClass(selectedTab);
    document.querySelectorAll('.pricing-item').forEach(item=>{
        item.classList.remove('personal','professional','flagship');
        item.classList.add(versionClass);
    });
    updateOrderSummary();
}
function updateOrderSummary(){
    var selectedVersion=document.querySelector('.version-tab.active .tab-name').textContent;
    var selectedPricing=document.querySelector('.pricing-item.selected');
    if(selectedPricing){
        var planName=selectedPricing.getAttribute('data-name');
        var price=parseFloat(selectedPricing.getAttribute('data-price'));
        var originalPrice=parseFloat(selectedPricing.getAttribute('data-original'));
        var desc=selectedPricing.getAttribute('data-desc');
        var discount=(originalPrice-price).toFixed(0);
        document.getElementById('selectedPlan').textContent=planName+selectedVersion;
        document.getElementById('selectedDesc').textContent=desc||'';
        var discountInfo=document.querySelector('.discount-info');
        var originalTotal=document.querySelector('.original-total');
        var currentTotal=document.querySelector('.current-total');
        var discountAmount=document.getElementById('discountAmount');
        if(discountInfo)discountInfo.textContent='已优惠: ¥'+discount;
        if(originalTotal)originalTotal.textContent='¥'+originalPrice.toFixed(0);
        if(currentTotal)currentTotal.textContent='¥'+price.toFixed(0);
        if(discountAmount)discountAmount.innerHTML='<i class="fa fa-clock" style="margin-right:4px;animation:pulse 2s infinite;"></i>限时节省¥'+discount;
    }
}
function submitUpgrade(){
var selectedVersion=document.querySelector('.version-tab.active .tab-name').textContent;
var selectedPricing=document.querySelector('.pricing-item.selected');

    if(!selectedPricing){
        alert('请选择订阅方式！');
        return;
    }
    var accountInput=document.getElementById('txtAccount');
    var account=currentAccount||(accountInput?accountInput.value.trim():'');
    if(!account){
        var accountSection=document.getElementById('accountInput');
        if(accountSection)accountSection.style.display='block';
        alert('请输入您要升级的账号（手机号/邮箱）！');
        if(accountInput)accountInput.focus();
        return;
    }
    if(!phoneReg.test(account)&&!emailReg.test(account)){
        alert("账号格式有误（手机号/邮箱），请检查后重试！");
        if(accountInput)accountInput.focus();
        return;
    }
    var upgradeBtn=document.getElementById('upgradeBtn');
    var btnText=upgradeBtn.querySelector('.btn-text');
    upgradeBtn.classList.add('loading');
    btnText.textContent='处理中...';
    upgradeBtn.disabled=true;
    var planName=selectedPricing.getAttribute('data-name');
    var upgradeType=planName+selectedVersion;

    fetch("code.aspx?op=pay&remark="+encodeURIComponent(upgradeType)+"&account="+encodeURIComponent(account))
        .then(response=>response.text())
        .then(result=>{
            if(result.indexOf('http')!==-1){
                window.location.href=result.replace("http:","").replace("https:","");
            }else{
                alert(result);
                resetUpgradeButton();
            }
        }).catch((error)=>{
            alert('请求失败，请稍后重试！');
            resetUpgradeButton();
        });
}

function resetUpgradeButton(){
    var upgradeBtn=document.getElementById('upgradeBtn');
    var btnText=upgradeBtn.querySelector('.btn-text');
    upgradeBtn.classList.remove('loading');
    btnText.textContent='立即升级';
    upgradeBtn.disabled=false;
}

function openCompareModal(){document.getElementById('compareModal').style.display='block';document.body.style.overflow='hidden'}
function closeCompareModal(){document.getElementById('compareModal').style.display='none';document.body.style.overflow='auto'}
window.onclick=function(event){var modal=document.getElementById('compareModal');if(event.target==modal){closeCompareModal()}}
document.addEventListener('keydown',function(event){if(event.key==='Escape'){closeCompareModal()}});

// 智能推荐Iframe相关函数
function openSmartRecommendationIframe() {
    // 创建Iframe模态框，样式与原版完全一致
    var iframeHtml = `
    <div id="smartRecommendationModal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;display:flex;align-items:center;justify-content:center;backdrop-filter:blur(3px);">
        <div style="background:transparent;border-radius:20px;padding:0;max-width:600px;width:90%;max-height:90vh;display:flex;flex-direction:column;box-shadow:0 20px 60px rgba(0,0,0,0.3);position:relative;overflow:hidden;">
            <iframe src="SmartRecommendation.aspx" style="width:100%;height:90vh;border:none;border-radius:20px;background:transparent;" frameborder="0"></iframe>
        </div>
    </div>`;

    document.body.insertAdjacentHTML('beforeend', iframeHtml);
    document.body.style.overflow = 'hidden';

    // 监听来自子页面的消息
    window.addEventListener('message', handleRecommendationMessage);
}

function closeSmartRecommendationIframe() {
    var modal = document.getElementById('smartRecommendationModal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
        window.removeEventListener('message', handleRecommendationMessage);
    }
}

function handleRecommendationMessage(event) {
    // 确保消息来源安全
    if (event.origin !== window.location.origin) return;

    var data = event.data;

    switch(data.action) {
        case 'closeRecommendation':
            closeSmartRecommendationIframe();
            break;

        case 'handleRecommendation':
            // 处理复杂的推荐逻辑（从原文件迁移）
            closeSmartRecommendationIframe();

            var pendingRecommendation = data.pendingRecommendation;
            if (pendingRecommendation) {
                if (!pendingRecommendation.userHasSelected) {
                    // 情况1：用户直接关闭窗口，自动应用推荐（需要弹框提示）
                    switchToVersionWithToast(pendingRecommendation.version, true);
                } else if (pendingRecommendation.isApplyRecommendation) {
                    // 情况2：用户点击"应用推荐"（需要弹框提示）
                    switchToVersionWithToast(pendingRecommendation.version, true);
                } else if (pendingRecommendation.selectedVersion) {
                    // 情况3：用户选择了其他版本（不需要弹框提示）
                    switchToVersionWithToast(pendingRecommendation.selectedVersion, false);
                }
            }
            break;
    }
}

function switchToVersionWithToast(versionType, showToast) {
    // 找到对应的版本标签（从原文件迁移）
    var versionMap = {
        'personal': '个人版',
        'professional': '专业版',
        'flagship': '旗舰版'
    };

    var versionName = versionMap[versionType];
    var versionTab = Array.from(document.querySelectorAll('.version-tab')).find(function(tab) {
        return tab.querySelector('.tab-name').textContent === versionName;
    });

    if (versionTab) {
        // 切换到指定版本
        if (!versionTab.classList.contains('active')) {
            versionTab.click();
        }

        // 根据参数决定是否显示弹框提示
        if (showToast) {
            showRecommendationToast(versionName);
        }

        // 滚动到版本选择区域
        versionTab.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// 保留原有函数作为兼容
function applyRecommendedVersion(version) {
    switchToVersionWithToast(version, true);
}

function showRecommendationToast(versionName) {
    // 移除可能存在的旧提示（从原文件迁移）
    var existingToast = document.querySelector('.recommendation-toast');
    if (existingToast) {
        existingToast.remove();
    }

    var toast = document.createElement('div');
    toast.className = 'recommendation-toast';
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        z-index: 10000;
        box-shadow: 0 8px 24px rgba(40, 167, 69, 0.3);
        max-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fa fa-check-circle" style="font-size: 16px;"></i>
            <div>
                <div>已为您选择 ${versionName}</div>
                <div style="font-size: 12px; opacity: 0.9; margin-top: 2px;">基于AI智能分析推荐</div>
            </div>
        </div>
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(function() {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 10);

    // 4秒后隐藏并移除
    setTimeout(function() {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(function() {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 4000);
}

// 添加必要的CSS动画
var style = document.createElement('style');
style.textContent = `
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
`;
document.head.appendChild(style);

    </script>
</asp:Content>
