<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能版本推荐</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
        }

        #smartRecommendationContainer {
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 智能推荐区域样式 - 增强版（从原文件迁移） */
        .smart-recommendation{
            background:linear-gradient(135deg,rgba(0,123,250,0.08) 0%,rgba(0,123,250,0.12) 100%);
            border-radius:16px;
            padding:20px;
            margin-bottom:24px;
            text-align:center;
            border:2px solid rgba(0,123,250,0.2);
            position:relative;
            overflow:hidden;
            transition:all 0.3s ease;
            box-shadow:0 4px 16px rgba(0,123,250,0.1);
        }

        .smart-recommendation::before {
            content:'';
            position:absolute;
            top:0;
            left:-100%;
            width:100%;
            height:100%;
            background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);
            animation:shimmer 3s infinite;
        }

        .smart-recommendation:hover {
            transform:translateY(-2px);
            box-shadow:0 8px 24px rgba(0,123,250,0.2);
            border-color:rgba(0,123,250,0.3);
        }

        .recommendation-trigger {
            display:flex;
            align-items:center;
            justify-content:center;
            gap:12px;
            flex-wrap:wrap;
        }

        .recommendation-trigger .fa-magic {
            animation:magicPulse 2s ease-in-out infinite;
        }

        /* 动画关键帧（从原文件迁移） */
        @keyframes shimmer {
            0% { left:-100%; }
            100% { left:100%; }
        }

        @keyframes magicPulse {
            0%, 100% {
                transform:scale(1) rotate(0deg);
                color:#007cfa;
            }
            50% {
                transform:scale(1.1) rotate(5deg);
                color:#0056b3;
            }
        }

        @keyframes celebrationPulse {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.05); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes fadeOutUp {
            0% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }

        @keyframes compactScale {
            0% { transform: scale(1); }
            100% { transform: scale(0.85); }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
            40%, 43% { transform: translateY(-15px); }
            70% { transform: translateY(-7px); }
            90% { transform: translateY(-3px); }
        }

        @keyframes scaleIn {
            0% { transform: scale(0.8); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes slideInUp {
            0% { transform: translateY(30px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideInLeft {
            0% { transform: translateX(-30px); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInRight {
            0% { transform: translateX(30px); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        @keyframes progressFill {
            0% { stroke-dashoffset: 314; }
            100% { stroke-dashoffset: var(--target-offset); }
        }

        @keyframes countUp {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes fadeIn{
            from{opacity:0}
            to{opacity:1}
        }

        @keyframes fadeInUp {
            0% { transform: translateY(20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        /* 问卷弹窗滚动条样式（从原文件迁移） */
        .quiz-body::-webkit-scrollbar{width:6px}
        .quiz-body::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}
        .quiz-body::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}
        .quiz-body::-webkit-scrollbar-thumb:hover{background:#a8a8a8}

        /* 使用场景标签样式（从原文件迁移） */
        .scenario-tags{
            display:flex;
            flex-wrap:wrap;
            gap:8px;
            margin-top:12px;
            justify-content:center;
        }

        .scenario-tag{
            background:rgba(0,123,250,0.1);
            color:#007cfa;
            padding:4px 12px;
            border-radius:16px;
            font-size:12px;
            font-weight:500;
            border:1px solid rgba(0,123,250,0.2);
            transition:all 0.3s ease;
        }

        .scenario-tag:hover{
            background:rgba(0,123,250,0.15);
            transform:translateY(-1px);
            box-shadow:0 2px 8px rgba(0,123,250,0.2);
        }

        /* 推荐结果移动端适配（从原文件迁移） */
        @media (max-width: 768px) {
            .recommendation-result {
                padding: 16px !important;
            }

            .recommended-version-card {
                padding: 16px !important;
                margin-bottom: 24px !important;
            }

            .version-header {
                margin-bottom: 16px !important;
            }

            .version-header div:first-child {
                font-size: 24px !important;
                margin-bottom: 12px !important;
            }

            .match-percentage {
                transform: scale(0.8);
            }

            .recommendation-reasons,
            .version-comparison,
            .user-profile,
            .alternatives,
            .action-section {
                margin-bottom: 24px !important;
            }

            .recommendation-reasons h4,
            .version-comparison h4,
            .user-profile h4 {
                font-size: 16px !important;
                margin-bottom: 16px !important;
            }

            .recommendation-reasons > div,
            .version-comparison > div,
            .user-profile > div:last-child {
                gap: 12px !important;
            }

            .action-section > div:first-child {
                padding: 16px !important;
                margin-bottom: 16px !important;
            }

            .action-section > div:last-child {
                gap: 12px !important;
            }

            .action-section button {
                padding: 12px 20px !important;
                font-size: 14px !important;
            }
        }

        /* 问卷问题动画样式 */
        .quiz-question {
            animation: fadeInUp 0.4s ease;
        }
    </style>
</head>
<body>
    <div id="smartRecommendationContainer">
        <!-- 问卷内容将通过JavaScript动态生成 -->
    </div>

    <script>
        // 智能推荐问卷数据 - 扩展版（从原文件完整迁移）
        var recommendationQuiz = [
            {
                id: 'usage_scenario',
                question: "您主要在什么场景下使用OCR功能？",
                type: 'single',
                options: [
                    {
                        text: "个人学习、偶尔使用",
                        desc: "扫描笔记、提取图片文字等轻度使用",
                        weight: { personal: 4, professional: 1, flagship: 0 },
                        tags: ['个人用户', '低频使用']
                    },
                    {
                        text: "日常办公、经常使用",
                        desc: "处理工作文档、合同等中等频率使用",
                        weight: { personal: 2, professional: 4, flagship: 2 },
                        tags: ['办公用户', '中频使用']
                    },
                    {
                        text: "专业工作、高频使用",
                        desc: "大量文档处理、数据提取等专业场景",
                        weight: { personal: 0, professional: 3, flagship: 4 },
                        tags: ['专业用户', '高频使用']
                    },
                    {
                        text: "团队协作、批量处理",
                        desc: "多人使用、批量文档处理等企业级应用",
                        weight: { personal: 0, professional: 2, flagship: 5 },
                        tags: ['企业用户', '批量处理']
                    }
                ]
            },
            {
                id: 'daily_usage',
                question: "您预计每天需要识别多少次？",
                type: 'single',
                options: [
                    {
                        text: "20次以内",
                        desc: "轻度使用，偶尔需要",
                        weight: { personal: 4, professional: 1, flagship: 0 },
                        tags: ['轻度使用']
                    },
                    {
                        text: "20-100次",
                        desc: "中等使用频率",
                        weight: { personal: 3, professional: 4, flagship: 1 },
                        tags: ['中度使用']
                    },
                    {
                        text: "100-500次",
                        desc: "较高使用频率",
                        weight: { personal: 1, professional: 4, flagship: 3 },
                        tags: ['高频使用']
                    },
                    {
                        text: "500次以上",
                        desc: "超高频使用或批量处理",
                        weight: { personal: 0, professional: 2, flagship: 5 },
                        tags: ['超高频使用']
                    }
                ]
            },
            {
                id: 'feature_needs',
                question: "您最需要哪些功能？（可多选）",
                type: 'multiple',
                options: [
                    {
                        text: "基础文字识别",
                        desc: "图片转文字的基本功能",
                        weight: { personal: 2, professional: 2, flagship: 1 },
                        tags: ['基础功能']
                    },
                    {
                        text: "表格识别",
                        desc: "识别表格结构和数据",
                        weight: { personal: 0, professional: 3, flagship: 3 },
                        tags: ['高级功能']
                    },
                    {
                        text: "公式识别",
                        desc: "数学公式、化学式等专业内容",
                        weight: { personal: 1, professional: 3, flagship: 3 },
                        tags: ['专业功能']
                    },
                    {
                        text: "批量处理",
                        desc: "一次处理多个文件",
                        weight: { personal: 0, professional: 2, flagship: 4 },
                        tags: ['效率功能']
                    },
                    {
                        text: "多格式转换",
                        desc: "支持多种输出格式",
                        weight: { personal: 1, professional: 2, flagship: 4 },
                        tags: ['格式功能']
                    },
                    {
                        text: "翻译功能",
                        desc: "识别后直接翻译",
                        weight: { personal: 2, professional: 2, flagship: 2 },
                        tags: ['语言功能']
                    }
                ]
            },
            {
                id: 'budget_preference',
                question: "您对价格的考虑是？",
                type: 'single',
                options: [
                    {
                        text: "价格优先，功能够用就行",
                        desc: "希望以最低成本满足基本需求",
                        weight: { personal: 4, professional: 1, flagship: 0 },
                        tags: ['价格敏感']
                    },
                    {
                        text: "性价比平衡，功能和价格都重要",
                        desc: "在合理价格范围内选择功能较全的版本",
                        weight: { personal: 2, professional: 4, flagship: 2 },
                        tags: ['性价比导向']
                    },
                    {
                        text: "功能优先，价格不是主要考虑",
                        desc: "愿意为更好的功能和体验付费",
                        weight: { personal: 1, professional: 2, flagship: 4 },
                        tags: ['功能导向']
                    }
                ]
            },
            {
                id: 'device_usage',
                question: "您通常在几台设备上使用？",
                type: 'single',
                options: [
                    {
                        text: "1台设备",
                        desc: "只在一台电脑上使用",
                        weight: { personal: 3, professional: 2, flagship: 1 },
                        tags: ['单设备']
                    },
                    {
                        text: "2-3台设备",
                        desc: "家里和办公室等多个地方使用",
                        weight: { personal: 2, professional: 3, flagship: 2 },
                        tags: ['多设备']
                    },
                    {
                        text: "3台以上设备",
                        desc: "团队使用或多个工作场所",
                        weight: { personal: 0, professional: 2, flagship: 4 },
                        tags: ['团队使用']
                    }
                ]
            },
            {
                id: 'user_type',
                question: "您的使用场景是？",
                type: 'single',
                options: [
                    {
                        text: "个人用户",
                        desc: "个人日常使用",
                        weight: { personal: 4, professional: 2, flagship: 1 },
                        tags: ['个人用户']
                    },
                    {
                        text: "企业员工",
                        desc: "公司工作需要",
                        weight: { personal: 1, professional: 4, flagship: 3 },
                        tags: ['企业用户']
                    },
                    {
                        text: "专业人士",
                        desc: "律师、会计师、研究员等专业工作",
                        weight: { personal: 0, professional: 3, flagship: 4 },
                        tags: ['专业用户']
                    }
                ]
            }
        ];

        // 全局变量初始化（从原文件迁移）
        var currentQuizStep = 0;
        var quizAnswers = {};
        var userProfile = {};

        // 启动智能推荐问卷（从原文件迁移）
        function startRecommendationQuiz() {
            currentQuizStep = 0;
            quizAnswers = {};
            userProfile = {};

            // 清除之前的推荐信息
            window.pendingRecommendation = null;

            var quizHtml = `
            <div class="quiz-modal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;display:flex;align-items:center;justify-content:center;backdrop-filter:blur(3px);">
                <div class="quiz-content" style="background:white;border-radius:20px;padding:0;max-width:600px;width:90%;max-height:90vh;display:flex;flex-direction:column;box-shadow:0 20px 60px rgba(0,0,0,0.3);">
                    <!-- 头部 -->
                    <div class="quiz-header" style="background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;padding:20px 24px;text-align:center;position:relative;flex-shrink:0;">
                        <button onclick="closeQuiz()" style="position:absolute;right:16px;top:16px;background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:18px;transition:all 0.3s ease;">×</button>
                        <h3 style="margin:0;font-size:20px;font-weight:600;">🎯 智能版本推荐</h3>
                        <p style="margin:8px 0 0;opacity:0.9;font-size:14px;">30秒找到最适合您的方案</p>
                        <!-- 进度条 -->
                        <div class="progress-bar" style="width:100%;height:4px;background:rgba(255,255,255,0.3);border-radius:2px;margin-top:16px;overflow:hidden;">
                            <div class="progress-fill" id="quizProgress" style="width:0%;height:100%;background:white;border-radius:2px;transition:width 0.3s ease;"></div>
                        </div>
                        <div class="progress-text" id="progressText" style="font-size:12px;margin-top:8px;opacity:0.8;">第 1 步，共 ${recommendationQuiz.length} 步</div>
                    </div>

                    <!-- 内容区域 - 可滚动 -->
                    <div class="quiz-body" style="padding:24px;flex:1;overflow-y:auto;min-height:0;">
                        <div id="quizQuestions"></div>
                    </div>

                    <!-- 底部操作区 -->
                    <div class="quiz-footer" style="padding:16px 24px;border-top:1px solid #f0f0f0;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;">
                        <button id="prevBtn" onclick="previousQuestion()" style="background:#f8f9fa;color:#666;border:1px solid #e9ecef;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;transition:all 0.3s ease;font-size:14px;">
                            ← 上一步
                        </button>
                        <div style="flex:1;"></div>
                        <button id="nextBtn" onclick="nextQuestion()" style="background:#007cfa;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;opacity:0.5;pointer-events:none;transition:all 0.3s ease;font-size:14px;">
                            下一步 →
                        </button>
                        <button id="finishBtn" onclick="showRecommendation()" style="background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;font-weight:600;font-size:14px;">
                            🎉 查看推荐
                        </button>
                    </div>
                </div>
            </div>`;

            document.body.insertAdjacentHTML('beforeend', quizHtml);
            showQuizQuestion(0);

            // 添加键盘事件监听
            document.addEventListener('keydown', handleQuizKeyboard);
        }

        // 显示问卷问题（从原文件迁移）
        function showQuizQuestion(index) {
            if (index >= recommendationQuiz.length) return;

            currentQuizStep = index;
            var question = recommendationQuiz[index];
            var progress = ((index + 1) / recommendationQuiz.length) * 100;

            // 更新进度条
            document.getElementById('quizProgress').style.width = progress + '%';
            document.getElementById('progressText').textContent = `第 ${index + 1} 步，共 ${recommendationQuiz.length} 步`;

            var html = `
            <div class="quiz-question" style="animation:fadeInUp 0.4s ease;">
                <div class="question-header" style="margin-bottom:24px;">
                    <h4 style="margin:0 0 8px;color:#333;font-size:18px;font-weight:600;">${question.question}</h4>
                    ${question.type === 'multiple' ? '<p style="margin:0;color:#666;font-size:14px;">💡 可以选择多个选项</p>' : ''}
                </div>

                <div class="options-container" style="display:grid;gap:12px;">`;

            question.options.forEach(function(option, i) {
                var inputType = question.type === 'multiple' ? 'checkbox' : 'radio';
                var inputName = question.id;
                var isSelected = quizAnswers[question.id] &&
                    (Array.isArray(quizAnswers[question.id]) ?
                        quizAnswers[question.id].includes(i) :
                        quizAnswers[question.id] === i);

                html += `
                <label class="option-item" style="display:block;padding:16px;border:2px solid #e9ecef;border-radius:12px;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;${isSelected ? 'border-color:#007cfa;background:rgba(0,123,250,0.05);' : ''}"
                       onmouseover="this.style.borderColor='#007cfa';this.style.background='rgba(0,123,250,0.05)'"
                       onmouseout="if(!this.querySelector('input').checked) {this.style.borderColor='#e9ecef';this.style.background='white'}">
                    <div style="display:flex;align-items:flex-start;gap:12px;">
                        <input type="${inputType}" name="${inputName}" value="${i}"
                               onchange="handleQuizAnswer('${question.id}', ${i}, '${inputType}')"
                               style="margin-top:2px;transform:scale(1.2);" ${isSelected ? 'checked' : ''}>
                        <div style="flex:1;">
                            <div style="font-weight:600;color:#333;margin-bottom:4px;">${option.text}</div>
                            <div style="font-size:13px;color:#666;line-height:1.4;">${option.desc}</div>
                            <div style="margin-top:8px;">
                                ${option.tags.map(tag => `<span style="display:inline-block;background:rgba(0,123,250,0.1);color:#007cfa;padding:2px 8px;border-radius:12px;font-size:11px;margin-right:6px;">${tag}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                </label>`;
            });

            html += `
                </div>
            </div>`;

            document.getElementById('quizQuestions').innerHTML = html;

            // 更新按钮状态
            updateNavigationButtons();
        }

        // 处理问卷答案（从原文件迁移）
        function handleQuizAnswer(questionId, optionIndex, inputType) {
            var question = recommendationQuiz.find(q => q.id === questionId);
            var option = question ? question.options[optionIndex] : null;

            if (inputType === 'checkbox') {
                if (!quizAnswers[questionId]) {
                    quizAnswers[questionId] = [];
                }
                var index = quizAnswers[questionId].indexOf(optionIndex);
                if (index > -1) {
                    quizAnswers[questionId].splice(index, 1);
                } else {
                    quizAnswers[questionId].push(optionIndex);
                }
            } else {
                quizAnswers[questionId] = optionIndex;
            }

            updateNavigationButtons();

            // 自动进入下一题（仅单选题）
            if (inputType === 'radio') {
                setTimeout(function() {
                    if (currentQuizStep < recommendationQuiz.length - 1) {
                        nextQuestion();
                    }
                }, 800);
            }
        }

        // 更新导航按钮状态（从原文件迁移）
        function updateNavigationButtons() {
            var prevBtn = document.getElementById('prevBtn');
            var nextBtn = document.getElementById('nextBtn');
            var finishBtn = document.getElementById('finishBtn');

            // 检查当前问题是否有答案
            var hasAnswer = quizAnswers[recommendationQuiz[currentQuizStep].id] !== undefined &&
                (Array.isArray(quizAnswers[recommendationQuiz[currentQuizStep].id]) ?
                    quizAnswers[recommendationQuiz[currentQuizStep].id].length > 0 :
                    true);

            // 上一步按钮
            if (currentQuizStep > 0) {
                prevBtn.style.display = 'block';
            } else {
                prevBtn.style.display = 'none';
            }

            if (currentQuizStep === recommendationQuiz.length - 1) {
                // 最后一题
                nextBtn.style.display = 'none';
                finishBtn.style.display = 'block';
                if (hasAnswer) {
                    finishBtn.style.opacity = '1';
                    finishBtn.style.pointerEvents = 'auto';
                } else {
                    finishBtn.style.opacity = '0.5';
                    finishBtn.style.pointerEvents = 'none';
                }
            } else {
                // 非最后一题
                nextBtn.style.display = 'block';
                finishBtn.style.display = 'none';
                if (hasAnswer) {
                    nextBtn.style.opacity = '1';
                    nextBtn.style.pointerEvents = 'auto';
                } else {
                    nextBtn.style.opacity = '0.5';
                    nextBtn.style.pointerEvents = 'none';
                }
            }
        }

        // 下一题（从原文件迁移）
        function nextQuestion() {
            if (currentQuizStep < recommendationQuiz.length - 1) {
                showQuizQuestion(currentQuizStep + 1);
            }
        }

        // 上一题（从原文件迁移）
        function previousQuestion() {
            if (currentQuizStep > 0) {
                showQuizQuestion(currentQuizStep - 1);
            }
        }

        // 键盘事件处理（从原文件迁移）
        function handleQuizKeyboard(event) {
            if (!document.querySelector('.quiz-modal')) return;

            if (event.key === 'Escape') {
                closeQuiz();
            } else if (event.key === 'ArrowRight' || event.key === 'Enter') {
                var nextBtn = document.getElementById('nextBtn');
                var finishBtn = document.getElementById('finishBtn');
                if (nextBtn && nextBtn.style.display !== 'none' && nextBtn.style.pointerEvents !== 'none') {
                    nextQuestion();
                } else if (finishBtn && finishBtn.style.display !== 'none' && finishBtn.style.pointerEvents !== 'none') {
                    showRecommendation();
                }
            } else if (event.key === 'ArrowLeft') {
                if (currentQuizStep > 0) {
                    previousQuestion();
                }
            }
        }

        // 显示推荐结果（从原文件迁移）
        function showRecommendation() {
            // 计算推荐结果
            var recommendation = calculateRecommendation();

            // 显示推荐结果页面
            showRecommendationResult(recommendation);
        }

        // 计算推荐结果（从原文件完整迁移）
        function calculateRecommendation() {
            var scores = { personal: 0, professional: 0, flagship: 0 };
            var reasons = { personal: [], professional: [], flagship: [] };
            var userTags = [];

            // 遍历所有答案，计算权重分数
            for (var questionId in quizAnswers) {
                var question = recommendationQuiz.find(q => q.id === questionId);
                if (!question) continue;

                var answers = Array.isArray(quizAnswers[questionId]) ? quizAnswers[questionId] : [quizAnswers[questionId]];

                answers.forEach(function(answerIndex) {
                    var option = question.options[answerIndex];
                    if (!option) return;

                    // 累加权重分数
                    scores.personal += option.weight.personal || 0;
                    scores.professional += option.weight.professional || 0;
                    scores.flagship += option.weight.flagship || 0;

                    // 收集用户标签
                    userTags = userTags.concat(option.tags);

                    // 收集推荐理由
                    if (option.weight.personal > 2) {
                        reasons.personal.push(`${option.text} - 适合个人版的轻量化需求`);
                    }
                    if (option.weight.professional > 2) {
                        reasons.professional.push(`${option.text} - 专业版能更好满足您的需求`);
                    }
                    if (option.weight.flagship > 2) {
                        reasons.flagship.push(`${option.text} - 旗舰版提供最完整的解决方案`);
                    }
                });
            }

            // 构建用户画像
            userProfile = {
                tags: [...new Set(userTags)], // 去重
                scores: scores,
                answers: quizAnswers
            };

            // 确定推荐版本
            var maxScore = Math.max(scores.personal, scores.professional, scores.flagship);
            var recommendedVersion = 'personal';

            if (scores.flagship === maxScore) {
                recommendedVersion = 'flagship';
            } else if (scores.professional === maxScore) {
                recommendedVersion = 'professional';
            }

            // 计算匹配度 - 优化算法，增加区分度
            var matchPercentage = 60; // 降低默认值

            try {
                if (maxScore > 0) {
                    var totalScore = scores.personal + scores.professional + scores.flagship;
                    var scoreRatio = totalScore > 0 ? (maxScore / totalScore) : 0;

                    // 计算答题完整度（回答了几个问题）
                    var answeredQuestions = Object.keys(quizAnswers).length;
                    var completionRatio = answeredQuestions / recommendationQuiz.length;

                    // 基础匹配度：根据最高分数和完整度计算
                    var baseScore = Math.min(maxScore, 25); // 限制最高分数影响
                    var scorePercentage = (baseScore / 25) * 100; // 转换为百分比

                    // 基础匹配度：50-85%之间
                    matchPercentage = Math.round(50 + (scorePercentage * 0.35));

                    // 根据得分优势调整（领先程度）
                    var secondScore = 0;
                    var sortedScores = [scores.personal, scores.professional, scores.flagship].sort((a, b) => b - a);
                    if (sortedScores.length > 1) {
                        secondScore = sortedScores[1];
                    }
                    var advantage = maxScore - secondScore;

                    // 优势越大，匹配度越高
                    if (advantage >= 8) {
                        matchPercentage += 12; // 明显优势
                    } else if (advantage >= 5) {
                        matchPercentage += 8;  // 较大优势
                    } else if (advantage >= 3) {
                        matchPercentage += 5;  // 一般优势
                    } else if (advantage >= 1) {
                        matchPercentage += 2;  // 微弱优势
                    }

                    // 完整度奖励
                    matchPercentage += Math.round(completionRatio * 8);

                    // 添加小幅随机因子（减少随机性）
                    var confidenceBoost = Math.floor(Math.random() * 4) + 1; // 1-4的随机值
                    matchPercentage += confidenceBoost;

                    // 确保在合理范围内，增加区分度
                    matchPercentage = Math.max(65, Math.min(96, matchPercentage));
                } else {
                    // 如果没有得分，给一个中等匹配度
                    matchPercentage = 72;
                }
            } catch (e) {
                console.log('匹配度计算出错，使用默认值:', e);
                matchPercentage = 75; // 出错时的兜底值
            }

            // 生成个性化推荐理由
            var personalizedReasons = generatePersonalizedReasons(recommendedVersion, userProfile);

            return {
                version: recommendedVersion,
                scores: scores,
                matchPercentage: matchPercentage,
                reasons: personalizedReasons,
                userProfile: userProfile,
                alternatives: getAlternativeRecommendations(scores, recommendedVersion)
            };
        }

        // 生成个性化推荐理由（从原文件迁移）
        function generatePersonalizedReasons(version, profile) {
            var reasons = [];
            var versionNames = {
                'personal': '个人版',
                'professional': '专业版',
                'flagship': '旗舰版'
            };

            // 基于用户标签生成个性化理由
            if (profile.tags.includes('高频使用')) {
                if (version === 'flagship') {
                    reasons.push('⚡ 您的高频使用需求，旗舰版的2000次/日额度和极速处理能力最适合');
                } else if (version === 'professional') {
                    reasons.push('⚡ 您的使用频率较高，专业版的500次/日额度能很好满足需求');
                }
            }

            if (profile.tags.includes('专业功能')) {
                reasons.push('🔬 您需要的专业功能如公式识别、表格处理，' + versionNames[version] + '都能完美支持');
            }

            if (profile.tags.includes('批量处理')) {
                reasons.push('📊 批量处理是您的核心需求，' + versionNames[version] + '的批量功能将大大提升您的工作效率');
            }

            if (profile.tags.includes('价格敏感')) {
                reasons.push('💰 考虑到您对价格的关注，' + versionNames[version] + '在功能和价格之间达到了最佳平衡');
            }

            if (profile.tags.includes('企业用户')) {
                reasons.push('🏢 作为企业用户，' + versionNames[version] + '的多设备授权和专业支持能满足团队协作需求');
            }

            // 如果理由不足，添加通用理由
            if (reasons.length < 2) {
                switch(version) {
                    case 'personal':
                        reasons.push('✨ 个人版包含所有基础功能，性价比最高');
                        reasons.push('🎯 适合个人用户的日常文字识别需求');
                        break;
                    case 'professional':
                        reasons.push('⚖️ 专业版在功能和价格间达到完美平衡');
                        reasons.push('🚀 提供更多高级功能，提升工作效率');
                        break;
                    case 'flagship':
                        reasons.push('👑 旗舰版提供最完整的功能体验');
                        reasons.push('🎪 适合对功能要求较高的专业用户');
                        break;
                }
            }

            return reasons.slice(0, 4); // 最多返回4个理由
        }

        // 获取备选推荐（从原文件迁移）
        function getAlternativeRecommendations(scores, recommended) {
            var alternatives = [];
            var sortedVersions = Object.keys(scores).sort((a, b) => scores[b] - scores[a]);

            sortedVersions.forEach(function(version) {
                if (version !== recommended && scores[version] > 0) {
                    var reason = '';
                    switch(version) {
                        case 'personal':
                            reason = '如果预算有限，个人版也能满足基本需求';
                            break;
                        case 'professional':
                            reason = '如果需要更多专业功能，专业版是不错的选择';
                            break;
                        case 'flagship':
                            reason = '如果追求最佳体验，旗舰版功能最全面';
                            break;
                    }
                    alternatives.push({
                        version: version,
                        score: scores[version],
                        reason: reason
                    });
                }
            });

            return alternatives.slice(0, 2); // 最多返回2个备选方案
        }

        // 辅助函数：获取理由分类（从原文件迁移）
        function getReasonCategory(reason, tags) {
            if (reason.includes('价格') || reason.includes('性价比') || reason.includes('经济')) {
                return '💰 性价比分析';
            } else if (reason.includes('功能') || reason.includes('需求') || reason.includes('适合')) {
                return '🎯 需求匹配';
            } else if (reason.includes('使用') || reason.includes('频率') || reason.includes('次数')) {
                return '📊 使用分析';
            } else {
                return '✨ 智能推荐';
            }
        }

        // 辅助函数：生成用户画像总结（从原文件迁移）
        function generateUserSummary(tags, recommendedVersion) {
            var summaries = {
                'personal': {
                    '企业用户': '您是注重效率的职场人士，个人版能满足您的日常工作需求',
                    '专业用户': '作为专业人士，个人版为您提供了经济实用的解决方案',
                    '个人用户': '您是理性的个人用户，个人版的功能配置最符合您的使用习惯',
                    'default': '您是追求性价比的理性用户，个人版是您的最佳选择'
                },
                'professional': {
                    '企业用户': '您是高效的企业用户，专业版的强大功能助力您的工作',
                    '专业用户': '作为专业人士，专业版的高级功能正是您所需要的',
                    '个人用户': '您对功能有更高要求，专业版能满足您的进阶需求',
                    'default': '您是追求功能与性价比平衡的用户，专业版最适合您'
                },
                'flagship': {
                    '企业用户': '您是追求极致效率的企业精英，旗舰版为您提供顶级体验',
                    '专业用户': '作为资深专业人士，旗舰版的全功能配置是您的不二选择',
                    '个人用户': '您对品质有极高要求，旗舰版能满足您的所有需求',
                    'default': '您是追求极致体验的用户，旗舰版为您提供最强大的功能'
                }
            };

            var versionSummaries = summaries[recommendedVersion];
            for (var tag of tags) {
                if (versionSummaries[tag]) {
                    return versionSummaries[tag];
                }
            }
            return versionSummaries.default;
        }

        // 辅助函数：获取标签贡献度（从原文件迁移）
        function getTagContribution(tag, recommendedVersion) {
            var contributions = {
                '个人用户': { weight: 15, percentage: 75, description: '日常使用场景匹配' },
                '企业用户': { weight: 20, percentage: 85, description: '工作效率需求匹配' },
                '专业用户': { weight: 25, percentage: 95, description: '专业功能需求匹配' },
                '高频使用': { weight: 18, percentage: 80, description: '使用频率分析' },
                '团队使用': { weight: 22, percentage: 90, description: '协作需求匹配' },
                '预算敏感': { weight: 12, percentage: 60, description: '性价比考量' },
                '功能需求': { weight: 20, percentage: 85, description: '功能匹配度' }
            };

            return contributions[tag] || { weight: 10, percentage: 50, description: '综合因素考量' };
        }

        // 关闭问卷（从原文件迁移）
        function closeQuiz() {
            var modal = document.querySelector('.quiz-modal');
            if (modal) {
                modal.remove();
                document.removeEventListener('keydown', handleQuizKeyboard);

                // 延迟一点时间让页面重新布局，然后处理版本切换
                setTimeout(function() {
                    // 通知父页面处理推荐结果
                    if (window.parent && window.parent !== window && window.pendingRecommendation) {
                        var messageData = {
                            action: 'handleRecommendation',
                            pendingRecommendation: window.pendingRecommendation
                        };
                        window.parent.postMessage(messageData, '*');
                    } else if (window.parent && window.parent !== window) {
                        // 没有推荐信息，直接关闭
                        window.parent.postMessage({ action: 'closeRecommendation' }, '*');
                    }

                    // 清除待处理的推荐信息
                    window.pendingRecommendation = null;
                }, 200);
            }
        }

        // 应用推荐（从原文件迁移）
        function applyRecommendation(version) {
            // 标记为应用推荐（需要弹框提示）
            if (window.pendingRecommendation) {
                window.pendingRecommendation.isApplyRecommendation = true;
                window.pendingRecommendation.userHasSelected = true;
            }

            closeQuiz();
        }

        // 选择备选版本（从原文件迁移）
        function selectAlternativeVersion(version) {
            // 标记用户手动选择了其他版本（不需要弹框提示）
            if (window.pendingRecommendation) {
                window.pendingRecommendation.userHasSelected = true;
                window.pendingRecommendation.selectedVersion = version;
                window.pendingRecommendation.isApplyRecommendation = false;
            }

            // 关闭推荐界面
            closeQuiz();
        }

        // 重新开始问卷（从原文件迁移）
        function restartQuiz() {
            currentQuizStep = 0;
            quizAnswers = {};
            userProfile = {};

            // 清除待处理的推荐信息
            window.pendingRecommendation = null;

            showQuizQuestion(0);
        }

        // 显示推荐结果页面（从原文件完整迁移）
        function showRecommendationResult(recommendation) {
            // 保存推荐对象到全局变量，供动画函数使用
            window.currentRecommendation = recommendation;

            var versionNames = {
                'personal': '个人版',
                'professional': '专业版',
                'flagship': '旗舰版'
            };

            var versionColors = {
                'personal': '#6666FF',
                'professional': '#4B4B4B',
                'flagship': '#E6D700'
            };

            var recommendedVersionName = versionNames[recommendation.version];
            var primaryColor = versionColors[recommendation.version];

            // 生成节省时间提示
            var timeSaved = Math.floor(Math.random() * 10) + 15; // 15-25分钟

            var resultHtml = `
            <div class="recommendation-result" style="padding:24px;text-align:center;">
                <!-- 庆祝动画和成就感 -->
                <div class="celebration-header" style="margin-bottom:32px;animation:celebrationPulse 1s ease-out;text-align:center;">
                    <div style="font-size:64px;margin-bottom:16px;animation:bounce 1.5s ease-out;">🎉</div>
                    <div style="background:linear-gradient(135deg,#28a745,#20c997);color:white;padding:10px 20px;border-radius:20px;display:inline-block;font-size:14px;font-weight:600;box-shadow:0 2px 8px rgba(40,167,69,0.3);">
                        ✨ 分析完成！为您节省了 ${timeSaved} 分钟选择时间
                    </div>

                </div>

                <!-- 推荐版本卡片 -->
                <div class="recommended-version-card" style="background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border:2px solid ${primaryColor}30;border-radius:20px;padding:24px;margin-bottom:32px;position:relative;animation:scaleIn 0.8s ease-out 0.3s both;">
                    <!-- AI推荐徽章 -->
                    <div style="position:absolute;top:-12px;right:20px;background:linear-gradient(135deg,#FF6B6B,#FF8E8E);color:white;padding:6px 12px;border-radius:12px;font-size:11px;font-weight:700;box-shadow:0 2px 8px rgba(255,107,107,0.4);">
                        🤖 AI智能推荐
                    </div>

                    <!-- 版本名称和匹配度布局 -->
                    <div class="version-header" style="text-align:center;margin-bottom:20px;">
                        <!-- 版本名称 -->
                        <div style="font-size:32px;font-weight:900;color:${primaryColor};text-shadow:0 2px 4px rgba(0,0,0,0.1);margin-bottom:16px;">
                            ${recommendedVersionName}
                        </div>

                        <!-- 匹配度圆形进度条 -->
                        <div class="match-percentage" style="display:inline-block;">
                            <div style="position:relative;width:100px;height:100px;">
                                <svg width="100" height="100" style="transform:rotate(-90deg);">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="6"/>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="${primaryColor}" stroke-width="6"
                                            stroke-dasharray="251" stroke-dashoffset="${251 - (251 * recommendation.matchPercentage / 100)}"
                                            style="animation:progressFill 2s ease-out 0.5s both;"/>
                                </svg>
                                <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;">
                                    <div style="font-size:20px;font-weight:900;color:${primaryColor};" data-target="${recommendation.matchPercentage}">${recommendation.matchPercentage}</div>
                                    <div id="match-label" style="font-size:10px;color:#666;font-weight:600;margin-top:2px;">匹配度</div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- 推荐理由 -->
                <div class="recommendation-reasons" style="margin-bottom:32px;text-align:left;animation:slideInUp 0.8s ease-out 0.6s both;">
                    <h4 style="text-align:center;margin:0 0 24px;color:#333;font-size:18px;display:flex;align-items:center;justify-content:center;gap:8px;">
                        <span style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-size:20px;">🧠</span>
                        AI分析结果
                    </h4>
                    <div style="display:flex;flex-direction:column;gap:16px;width:100%;max-width:500px;margin:0 auto;">
                        ${recommendation.reasons.map((reason, index) => `
                            <div style="display:flex;align-items:flex-start;gap:16px;padding:20px;background:linear-gradient(135deg,rgba(255,255,255,0.9),rgba(255,255,255,0.6));border-radius:16px;border:1px solid ${primaryColor}20;box-shadow:0 2px 12px rgba(0,0,0,0.08);animation:slideInLeft 0.6s ease-out ${0.8 + index * 0.1}s both;min-height:80px;width:100%;box-sizing:border-box;">
                                <div style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;flex-shrink:0;box-shadow:0 2px 8px ${primaryColor}40;">${index + 1}</div>
                                <div style="flex:1;display:flex;flex-direction:column;justify-content:center;min-width:0;">
                                    <div style="color:#333;line-height:1.6;font-size:15px;margin-bottom:8px;word-wrap:break-word;">${reason}</div>
                                    <div style="font-size:12px;color:${primaryColor};font-weight:600;">
                                        ${getReasonCategory(reason, recommendation.userProfile.tags)}
                                    </div>
                                </div>
                                <div style="color:${primaryColor};font-size:18px;opacity:0.7;align-self:center;flex-shrink:0;">✓</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- 适配度分析 -->
                <div class="version-comparison" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 0.9s both;">
                    <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                        <span style="font-size:20px;">📊</span>
                        适配度分析
                    </h4>
                    <div style="display:grid;gap:16px;max-width:450px;margin:0 auto;">
                        ${Object.keys(recommendation.scores).map((version, index) => {
                            var score = recommendation.scores[version];
                            var maxScore = Math.max(...Object.values(recommendation.scores));
                            var percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
                            var isRecommended = version === recommendation.version;

                            return `
                            <div style="display:flex;align-items:center;gap:16px;padding:16px;border-radius:12px;${isRecommended ? `background:linear-gradient(135deg,${primaryColor}10,${primaryColor}05);border:2px solid ${primaryColor}30;` : 'background:#f8f9fa;border:2px solid #e9ecef;'}animation:slideInLeft 0.6s ease-out ${1.1 + index * 0.1}s both;">
                                <div style="font-weight:600;color:#333;min-width:70px;font-size:15px;">${versionNames[version]}</div>
                                <div style="flex:1;background:#e9ecef;height:10px;border-radius:5px;overflow:hidden;">
                                    <div style="width:${percentage}%;height:100%;background:${isRecommended ? primaryColor : '#adb5bd'};border-radius:5px;transition:width 1s ease 0.5s;"></div>
                                </div>
                                <div style="font-weight:700;color:${isRecommended ? primaryColor : '#666'};min-width:45px;font-size:15px;">${score}分</div>
                                ${isRecommended ? `<div style="color:${primaryColor};font-size:18px;">⭐</div>` : ''}
                            </div>`;
                        }).join('')}
                    </div>
                </div>

                <!-- 用户画像分析 -->
                <div class="user-profile" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.1s both;">
                    <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                        <span style="font-size:20px;">👤</span>
                        您的专属画像
                    </h4>

                    <!-- 画像总结 -->
                    <div style="background:linear-gradient(135deg,rgba(255,255,255,0.95),rgba(255,255,255,0.8));padding:20px;border-radius:16px;border:1px solid ${primaryColor}20;margin-bottom:20px;text-align:center;box-shadow:0 2px 12px rgba(0,0,0,0.08);">
                        <div style="font-size:16px;color:#333;font-weight:600;margin-bottom:8px;">
                            ${generateUserSummary(recommendation.userProfile.tags, recommendation.version)}
                        </div>
                        <div style="font-size:13px;color:#666;">
                            基于您的选择，AI为您生成的个性化标签
                        </div>
                    </div>

                    <!-- 标签及其贡献度 -->
                    <div style="display:grid;gap:12px;max-width:500px;margin:0 auto;">
                        ${recommendation.userProfile.tags.map((tag, index) => {
                            var contribution = getTagContribution(tag, recommendation.version);
                            return `
                            <div style="display:flex;align-items:center;justify-content:space-between;padding:16px;background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border-radius:12px;border:1px solid ${primaryColor}15;animation:slideInRight 0.6s ease-out ${1.3 + index * 0.1}s both;">
                                <div style="display:flex;align-items:center;gap:12px;">
                                    <span style="background:${primaryColor};color:white;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;">${tag}</span>
                                    <span style="font-size:13px;color:#666;">${contribution.description}</span>
                                </div>
                                <div style="display:flex;align-items:center;gap:8px;">
                                    <div style="font-size:12px;color:${primaryColor};font-weight:600;">+${contribution.weight}分</div>
                                    <div style="width:40px;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden;">
                                        <div style="width:${contribution.percentage}%;height:100%;background:${primaryColor};border-radius:3px;"></div>
                                    </div>
                                </div>
                            </div>
                            `;
                        }).join('')}
                    </div>
                </div>`;

        // 显示推荐结果页面第3部分（从原文件迁移）
        function showRecommendationResultPart3(resultHtml, recommendation, primaryColor, recommendedVersionName, versionNames) {
            // 继续构建HTML - 备选方案和操作按钮
            resultHtml += `
                <!-- 备选方案 -->
                ${recommendation.alternatives.length > 0 ? `
                <div class="alternatives" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.5s both;">
                    <h4 style="margin:0 0 20px;color:#333;font-size:16px;text-align:center;">🤔 其他选择</h4>
                    <div style="display:flex;flex-direction:column;gap:12px;max-width:500px;margin:0 auto;">
                        ${recommendation.alternatives.map((alt, index) => `
                            <div onclick="selectAlternativeVersion('${alt.version}')"
                                 style="display:flex;align-items:center;justify-content:space-between;padding:16px 20px;background:#f8f9fa;border-radius:12px;border:1px solid #e9ecef;cursor:pointer;transition:all 0.3s ease;animation:slideInRight 0.6s ease-out ${1.7 + index * 0.15}s both;"
                                 onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#2196f3';this.style.transform='translateY(-1px)';this.style.boxShadow='0 2px 8px rgba(33,150,243,0.2)'"
                                 onmouseout="this.style.background='#f8f9fa';this.style.borderColor='#e9ecef';this.style.transform='translateY(0)';this.style.boxShadow='none'">
                                <div style="flex:1;">
                                    <div style="font-weight:600;color:#333;font-size:15px;margin-bottom:4px;">${versionNames[alt.version]}</div>
                                    <div style="font-size:13px;color:#666;line-height:1.4;">${alt.reason}</div>
                                </div>
                                <div style="color:#2196f3;font-size:18px;margin-left:12px;">→</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                <!-- 行动引导区域 -->
                <div class="action-section" style="text-align:center;animation:slideInUp 0.8s ease-out 2s both;">
                    <!-- 推荐提示 -->
                    <div style="background:linear-gradient(135deg,${primaryColor}12,${primaryColor}06);border-radius:16px;padding:24px;margin-bottom:24px;border:1px solid ${primaryColor}25;position:relative;overflow:hidden;">
                        <!-- 背景装饰 -->
                        <div style="position:absolute;top:-20px;right:-20px;width:80px;height:80px;background:${primaryColor}15;border-radius:50%;"></div>
                        <div style="position:absolute;bottom:-30px;left:-30px;width:100px;height:100px;background:${primaryColor}08;border-radius:50%;"></div>

                        <div style="position:relative;z-index:1;">
                            <div style="font-size:18px;color:#333;margin-bottom:12px;font-weight:600;">
                                <span style="color:${primaryColor};font-size:20px;">🎯</span>
                                推荐您选择 <strong style="color:${primaryColor};">${recommendedVersionName}</strong>
                            </div>

                            <!-- 保障信息 -->
                            <div style="display:flex;justify-content:center;gap:20px;flex-wrap:wrap;">
                                <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                                    <span style="color:#28a745;">✓</span>
                                    7天无理由退款
                                </div>
                                <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                                    <span style="color:#28a745;">✓</span>
                                    稳定服务保障
                                </div>
                                <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                                    <span style="color:#28a745;">✓</span>
                                    专业客服支持
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 按钮组 -->
                    <div style="display:flex;justify-content:center;gap:16px;flex-wrap:wrap;">
                        <button onclick="applyRecommendation('${recommendation.version}')"
                                style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;border:none;padding:16px 32px;border-radius:25px;font-size:16px;font-weight:700;cursor:pointer;box-shadow:0 4px 16px ${primaryColor}40;transition:all 0.3s ease;position:relative;overflow:hidden;"
                                onmouseover="this.style.transform='translateY(-2px)';this.style.boxShadow='0 6px 20px ${primaryColor}50'"
                                onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 4px 16px ${primaryColor}40'">
                            <span style="position:relative;z-index:1;">🚀 立即选择${recommendedVersionName}</span>
                        </button>

                        <button onclick="restartQuiz()"
                                style="background:linear-gradient(135deg,#6c757d,#5a6268);color:white;border:none;padding:14px 24px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;box-shadow:0 2px 8px rgba(108,117,125,0.3);transition:all 0.3s ease;"
                                onmouseover="this.style.transform='translateY(-1px)';this.style.boxShadow='0 4px 12px rgba(108,117,125,0.4)'"
                                onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 2px 8px rgba(108,117,125,0.3)'">
                            🔄 重新推荐
                        </button>

                        <button onclick="closeQuiz()"
                                style="background:transparent;color:#6c757d;border:2px solid #dee2e6;padding:12px 20px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.3s ease;"
                                onmouseover="this.style.color='#495057';this.style.borderColor='#adb5bd';this.style.background='#f8f9fa'"
                                onmouseout="this.style.color='#6c757d';this.style.borderColor='#dee2e6';this.style.background='transparent'">
                            关闭
                        </button>
                    </div>
                </div>
            </div>`;

            // 更新问卷内容
            document.getElementById('quizQuestions').innerHTML = resultHtml;

            // 隐藏导航按钮
            document.getElementById('prevBtn').style.display = 'none';
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('finishBtn').style.display = 'none';

            // 更新头部信息
            document.querySelector('.quiz-header h3').innerHTML = '🎉 推荐结果';
            document.querySelector('.quiz-header p').innerHTML = '基于您的需求分析得出的个性化推荐';
            document.getElementById('quizProgress').style.width = '100%';
            document.getElementById('progressText').textContent = '分析完成';

            // 保存推荐结果到本地存储（从原文件迁移）
            try {
                localStorage.setItem('ocrRecommendation', JSON.stringify({
                    result: recommendation,
                    timestamp: Date.now()
                }));
            } catch(e) {
                console.log('无法保存推荐结果到本地存储');
            }

            // 保存推荐版本信息，用于关闭时自动切换（从原文件迁移）
            window.pendingRecommendation = {
                version: recommendation.version,
                userHasSelected: false, // 标记用户是否手动选择了版本
                selectedVersion: null, // 用户手动选择的版本
                isApplyRecommendation: false // 是否是点击"应用推荐"按钮
            };

            // 启动数字动画 - 确保DOM渲染完成后再执行
            setTimeout(function() {
                animateCountUp();
                // 清理动画样式
                cleanupAnimations();

                // 2-3秒后隐藏庆祝动画并缩小匹配度显示
                var hideDelay = Math.floor(Math.random() * 1000) + 2000; // 2-3秒随机延迟
                setTimeout(function() {
                    hideCelebrationAndCompactView();
                }, hideDelay);
            }, 1200); // 增加延迟时间，确保DOM完全渲染
        }

        // 数字动画（从原文件迁移）
        function animateCountUp() {
            var countElement = document.querySelector('[data-target]');
            if (!countElement) {
                return;
            }

            // 直接从全局推荐对象获取匹配度
            var target = 85; // 默认值
            if (window.currentRecommendation && window.currentRecommendation.matchPercentage) {
                target = parseInt(window.currentRecommendation.matchPercentage);
            }

            // 重置显示为0，开始动画
            countElement.textContent = '0';

            var current = 0;
            var increment = target / 50; // 约1.5秒内完成，50帧
            var timer = setInterval(function() {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                countElement.textContent = Math.floor(current);
            }, 30); // 约33fps，更流畅
        }

        // 清理动画样式（从原文件迁移）
        function cleanupAnimations() {
            setTimeout(function() {
                // 移除所有动画样式，避免影响后续操作
                var animatedElements = document.querySelectorAll('[style*="animation"]');
                animatedElements.forEach(function(element) {
                    var style = element.getAttribute('style');
                    if (style) {
                        // 移除animation相关样式，保留其他样式
                        var newStyle = style.replace(/animation:[^;]*;?/g, '');
                        element.setAttribute('style', newStyle);
                    }
                });
            }, 3000);
        }

        // 隐藏庆祝动画并缩小匹配度显示（从原文件迁移）
        function hideCelebrationAndCompactView() {
            // 先获取数据，再隐藏元素
            var versionHeader = document.querySelector('.version-header');
            var recommendedVersion = '个人版'; // 默认值
            var matchPercentage = '95%'; // 默认值

            // 从版本头部获取数据
            if (versionHeader) {
                var versionTitle = versionHeader.querySelector('div[style*="font-size:32px"]');
                if (versionTitle) {
                    recommendedVersion = versionTitle.textContent || '个人版';
                }

                var matchCircle = versionHeader.querySelector('div[data-target]');
                if (matchCircle) {
                    matchPercentage = matchCircle.textContent || matchCircle.getAttribute('data-target') || '95%';
                }
            }

            // 隐藏庆祝头部 - 使用更平滑的动画
            var celebrationHeader = document.querySelector('.celebration-header');
            if (celebrationHeader) {
                celebrationHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                celebrationHeader.style.opacity = '0';
                celebrationHeader.style.transform = 'translateY(-8px) scale(0.98)';

                // 完全隐藏元素
                setTimeout(function() {
                    celebrationHeader.style.display = 'none';
                }, 400);
            }

            // 完全隐藏推荐版本卡片
            var versionCard = document.querySelector('.recommended-version-card');
            if (versionCard) {
                versionCard.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                versionCard.style.opacity = '0';
                versionCard.style.transform = 'translateY(-20px) scale(0.95)';
                setTimeout(function() {
                    versionCard.style.display = 'none';
                }, 400);
            }

            // 隐藏原有的版本头部区域
            if (versionHeader) {
                versionHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                versionHeader.style.opacity = '0';
                versionHeader.style.transform = 'translateY(-10px)';
                setTimeout(function() {
                    versionHeader.style.display = 'none';
                }, 400);
            }
        }

        // 页面加载完成后自动启动问卷
        document.addEventListener('DOMContentLoaded', function() {
            startRecommendationQuiz();
        });
    </script>
</body>
</html>